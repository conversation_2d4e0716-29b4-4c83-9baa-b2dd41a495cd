version = 1
SPDX-PackageName = "dde-launchpad"
SPDX-PackageSupplier = "UnionTech Software Technology Co., Ltd.  <>"
SPDX-PackageDownloadLocation = "https://github.com/linuxdeepin/dde-launchpad"

[[annotations]]
path = ["debian/**", "rpm/**", "archlinux/**", ".packit.yaml"]
precedence = "aggregate"
SPDX-FileCopyrightText = "None"
SPDX-License-Identifier = "CC0-1.0"

[[annotations]]
path = [".github/**", ".obs/**", ".tx/**"]
precedence = "aggregate"
SPDX-FileCopyrightText = "None"
SPDX-License-Identifier = "CC0-1.0"

[[annotations]]
path = ["README.md", "README.**.md", "docs/**.md"]
precedence = "aggregate"
SPDX-FileCopyrightText = "UnionTech Software Technology Co., Ltd."
SPDX-License-Identifier = "CC-BY-4.0"

[[annotations]]
path = ["images/**.dci", "images/**.svg", "shell-launcher-applet/package/icons/**/**.dci", "shell-launcher-applet/package/icons/**.dci"]
precedence = "aggregate"
SPDX-FileCopyrightText = "UnionTech Software Technology Co., Ltd."
SPDX-License-Identifier = "GPL-3.0-or-later"

[[annotations]]
path = "**.qrc"
precedence = "aggregate"
SPDX-FileCopyrightText = "None"
SPDX-License-Identifier = "CC0-1.0"

[[annotations]]
path = "**/org.deepin.**.**.json"
precedence = "aggregate"
SPDX-FileCopyrightText = "None"
SPDX-License-Identifier = "CC0-1.0"

[[annotations]]
path = ["translations/**.ts", "shell-launcher-applet/translations/**.ts"]
precedence = "aggregate"
SPDX-FileCopyrightText = "UnionTech Software Technology Co., Ltd."
SPDX-License-Identifier = "GPL-3.0-or-later"

[[annotations]]
path = ["**.deepin.dde.**.xml", "**/org.desktopspec.**.xml"]
precedence = "aggregate"
SPDX-FileCopyrightText = "UnionTech Software Technology Co., Ltd."
SPDX-License-Identifier = "GPL-3.0-or-later"

[[annotations]]
path = "**/metadata.json"
precedence = "aggregate"
SPDX-FileCopyrightText = "None"
SPDX-License-Identifier = "CC0-1.0"

[[annotations]]
path = "toolGenerate/**/**"
precedence = "aggregate"
SPDX-FileCopyrightText = "None"
SPDX-License-Identifier = "CC0-1.0"
