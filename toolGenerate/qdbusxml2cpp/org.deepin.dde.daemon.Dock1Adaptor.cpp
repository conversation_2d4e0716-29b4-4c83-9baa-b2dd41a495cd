/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-launchpad/src/ddeintegration/xml/org.deepin.dde.daemon.Dock1.xml -a ./dde-launchpad/toolGenerate/qdbusxml2cpp/org.deepin.dde.daemon.Dock1Adaptor -i ./dde-launchpad/toolGenerate/qdbusxml2cpp/org.deepin.dde.daemon.Dock1.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * Do not edit! All changes made to it will be lost.
 */

#include "./dde-launchpad/toolGenerate/qdbusxml2cpp/org.deepin.dde.daemon.Dock1Adaptor.h"
#include <QtCore/QMetaObject>
#include <QtCore/QByteArray>
#include <QtCore/QList>
#include <QtCore/QMap>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QVariant>

/*
 * Implementation of adaptor class Dock1Adaptor
 */

Dock1Adaptor::Dock1Adaptor(QObject *parent)
    : QDBusAbstractAdaptor(parent)
{
    // constructor
    setAutoRelaySignals(true);
}

Dock1Adaptor::~Dock1Adaptor()
{
    // destructor
}

int Dock1Adaptor::displayMode() const
{
    // get the value of property DisplayMode
    return qvariant_cast< int >(parent()->property("DisplayMode"));
}

void Dock1Adaptor::setDisplayMode(int value)
{
    // set the value of property DisplayMode
    parent()->setProperty("DisplayMode", QVariant::fromValue(value));
}

QStringList Dock1Adaptor::dockedApps() const
{
    // get the value of property DockedApps
    return qvariant_cast< QStringList >(parent()->property("DockedApps"));
}

QList<QDBusObjectPath> Dock1Adaptor::entries() const
{
    // get the value of property Entries
    return qvariant_cast< QList<QDBusObjectPath> >(parent()->property("Entries"));
}

QRect Dock1Adaptor::frontendWindowRect() const
{
    // get the value of property FrontendWindowRect
    return qvariant_cast< QRect >(parent()->property("FrontendWindowRect"));
}

int Dock1Adaptor::hideMode() const
{
    // get the value of property HideMode
    return qvariant_cast< int >(parent()->property("HideMode"));
}

void Dock1Adaptor::setHideMode(int value)
{
    // set the value of property HideMode
    parent()->setProperty("HideMode", QVariant::fromValue(value));
}

int Dock1Adaptor::hideState() const
{
    // get the value of property HideState
    return qvariant_cast< int >(parent()->property("HideState"));
}

uint Dock1Adaptor::hideTimeout() const
{
    // get the value of property HideTimeout
    return qvariant_cast< uint >(parent()->property("HideTimeout"));
}

void Dock1Adaptor::setHideTimeout(uint value)
{
    // set the value of property HideTimeout
    parent()->setProperty("HideTimeout", QVariant::fromValue(value));
}

uint Dock1Adaptor::iconSize() const
{
    // get the value of property IconSize
    return qvariant_cast< uint >(parent()->property("IconSize"));
}

void Dock1Adaptor::setIconSize(uint value)
{
    // set the value of property IconSize
    parent()->setProperty("IconSize", QVariant::fromValue(value));
}

double Dock1Adaptor::opacity() const
{
    // get the value of property Opacity
    return qvariant_cast< double >(parent()->property("Opacity"));
}

void Dock1Adaptor::setOpacity(double value)
{
    // set the value of property Opacity
    parent()->setProperty("Opacity", QVariant::fromValue(value));
}

int Dock1Adaptor::position() const
{
    // get the value of property Position
    return qvariant_cast< int >(parent()->property("Position"));
}

void Dock1Adaptor::setPosition(int value)
{
    // set the value of property Position
    parent()->setProperty("Position", QVariant::fromValue(value));
}

uint Dock1Adaptor::showTimeout() const
{
    // get the value of property ShowTimeout
    return qvariant_cast< uint >(parent()->property("ShowTimeout"));
}

void Dock1Adaptor::setShowTimeout(uint value)
{
    // set the value of property ShowTimeout
    parent()->setProperty("ShowTimeout", QVariant::fromValue(value));
}

uint Dock1Adaptor::windowMargin() const
{
    // get the value of property WindowMargin
    return qvariant_cast< uint >(parent()->property("WindowMargin"));
}

uint Dock1Adaptor::windowSize() const
{
    // get the value of property WindowSize
    return qvariant_cast< uint >(parent()->property("WindowSize"));
}

void Dock1Adaptor::setWindowSize(uint value)
{
    // set the value of property WindowSize
    parent()->setProperty("WindowSize", QVariant::fromValue(value));
}

uint Dock1Adaptor::windowSizeEfficient() const
{
    // get the value of property WindowSizeEfficient
    return qvariant_cast< uint >(parent()->property("WindowSizeEfficient"));
}

void Dock1Adaptor::setWindowSizeEfficient(uint value)
{
    // set the value of property WindowSizeEfficient
    parent()->setProperty("WindowSizeEfficient", QVariant::fromValue(value));
}

uint Dock1Adaptor::windowSizeFashion() const
{
    // get the value of property WindowSizeFashion
    return qvariant_cast< uint >(parent()->property("WindowSizeFashion"));
}

void Dock1Adaptor::setWindowSizeFashion(uint value)
{
    // set the value of property WindowSizeFashion
    parent()->setProperty("WindowSizeFashion", QVariant::fromValue(value));
}

void Dock1Adaptor::ActivateWindow(uint in0)
{
    // handle method call org.deepin.dde.daemon.Dock1.ActivateWindow
    QMetaObject::invokeMethod(parent(), "ActivateWindow", Q_ARG(uint, in0));
}

void Dock1Adaptor::CancelPreviewWindow()
{
    // handle method call org.deepin.dde.daemon.Dock1.CancelPreviewWindow
    QMetaObject::invokeMethod(parent(), "CancelPreviewWindow");
}

void Dock1Adaptor::CloseWindow(uint in0)
{
    // handle method call org.deepin.dde.daemon.Dock1.CloseWindow
    QMetaObject::invokeMethod(parent(), "CloseWindow", Q_ARG(uint, in0));
}

QStringList Dock1Adaptor::GetDockedAppsDesktopFiles()
{
    // handle method call org.deepin.dde.daemon.Dock1.GetDockedAppsDesktopFiles
    QStringList out0;
    QMetaObject::invokeMethod(parent(), "GetDockedAppsDesktopFiles", Q_RETURN_ARG(QStringList, out0));
    return out0;
}

QStringList Dock1Adaptor::GetEntryIDs()
{
    // handle method call org.deepin.dde.daemon.Dock1.GetEntryIDs
    QStringList out0;
    QMetaObject::invokeMethod(parent(), "GetEntryIDs", Q_RETURN_ARG(QStringList, out0));
    return out0;
}

QString Dock1Adaptor::GetPluginSettings()
{
    // handle method call org.deepin.dde.daemon.Dock1.GetPluginSettings
    QString out0;
    QMetaObject::invokeMethod(parent(), "GetPluginSettings", Q_RETURN_ARG(QString, out0));
    return out0;
}

bool Dock1Adaptor::IsDocked(const QString &in0)
{
    // handle method call org.deepin.dde.daemon.Dock1.IsDocked
    bool out0;
    QMetaObject::invokeMethod(parent(), "IsDocked", Q_RETURN_ARG(bool, out0), Q_ARG(QString, in0));
    return out0;
}

bool Dock1Adaptor::IsOnDock(const QString &in0)
{
    // handle method call org.deepin.dde.daemon.Dock1.IsOnDock
    bool out0;
    QMetaObject::invokeMethod(parent(), "IsOnDock", Q_RETURN_ARG(bool, out0), Q_ARG(QString, in0));
    return out0;
}

void Dock1Adaptor::MakeWindowAbove(uint in0)
{
    // handle method call org.deepin.dde.daemon.Dock1.MakeWindowAbove
    QMetaObject::invokeMethod(parent(), "MakeWindowAbove", Q_ARG(uint, in0));
}

void Dock1Adaptor::MaximizeWindow(uint in0)
{
    // handle method call org.deepin.dde.daemon.Dock1.MaximizeWindow
    QMetaObject::invokeMethod(parent(), "MaximizeWindow", Q_ARG(uint, in0));
}

void Dock1Adaptor::MergePluginSettings(const QString &in0)
{
    // handle method call org.deepin.dde.daemon.Dock1.MergePluginSettings
    QMetaObject::invokeMethod(parent(), "MergePluginSettings", Q_ARG(QString, in0));
}

void Dock1Adaptor::MinimizeWindow(uint in0)
{
    // handle method call org.deepin.dde.daemon.Dock1.MinimizeWindow
    QMetaObject::invokeMethod(parent(), "MinimizeWindow", Q_ARG(uint, in0));
}

void Dock1Adaptor::MoveEntry(int in0, int in1)
{
    // handle method call org.deepin.dde.daemon.Dock1.MoveEntry
    QMetaObject::invokeMethod(parent(), "MoveEntry", Q_ARG(int, in0), Q_ARG(int, in1));
}

void Dock1Adaptor::MoveWindow(uint in0)
{
    // handle method call org.deepin.dde.daemon.Dock1.MoveWindow
    QMetaObject::invokeMethod(parent(), "MoveWindow", Q_ARG(uint, in0));
}

void Dock1Adaptor::PreviewWindow(uint in0)
{
    // handle method call org.deepin.dde.daemon.Dock1.PreviewWindow
    QMetaObject::invokeMethod(parent(), "PreviewWindow", Q_ARG(uint, in0));
}

QString Dock1Adaptor::QueryWindowIdentifyMethod(uint in0)
{
    // handle method call org.deepin.dde.daemon.Dock1.QueryWindowIdentifyMethod
    QString out0;
    QMetaObject::invokeMethod(parent(), "QueryWindowIdentifyMethod", Q_RETURN_ARG(QString, out0), Q_ARG(uint, in0));
    return out0;
}

void Dock1Adaptor::RemovePluginSettings(const QString &in0, const QStringList &in1)
{
    // handle method call org.deepin.dde.daemon.Dock1.RemovePluginSettings
    QMetaObject::invokeMethod(parent(), "RemovePluginSettings", Q_ARG(QString, in0), Q_ARG(QStringList, in1));
}

bool Dock1Adaptor::RequestDock(const QString &in0, int in1)
{
    // handle method call org.deepin.dde.daemon.Dock1.RequestDock
    bool out0;
    QMetaObject::invokeMethod(parent(), "RequestDock", Q_RETURN_ARG(bool, out0), Q_ARG(QString, in0), Q_ARG(int, in1));
    return out0;
}

bool Dock1Adaptor::RequestUndock(const QString &in0)
{
    // handle method call org.deepin.dde.daemon.Dock1.RequestUndock
    bool out0;
    QMetaObject::invokeMethod(parent(), "RequestUndock", Q_RETURN_ARG(bool, out0), Q_ARG(QString, in0));
    return out0;
}

void Dock1Adaptor::SetFrontendWindowRect(int in0, int in1, int in2, int in3)
{
    // handle method call org.deepin.dde.daemon.Dock1.SetFrontendWindowRect
    QMetaObject::invokeMethod(parent(), "SetFrontendWindowRect", Q_ARG(int, in0), Q_ARG(int, in1), Q_ARG(int, in2), Q_ARG(int, in3));
}

void Dock1Adaptor::SetPluginSettings(const QString &in0)
{
    // handle method call org.deepin.dde.daemon.Dock1.SetPluginSettings
    QMetaObject::invokeMethod(parent(), "SetPluginSettings", Q_ARG(QString, in0));
}

