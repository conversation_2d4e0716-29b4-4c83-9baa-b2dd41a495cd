/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-launchpad/src/ddeintegration/xml/org.desktopspec.ApplicationManager1.Application.xml -a ./dde-launchpad/toolGenerate/qdbusxml2cpp/org.desktopspec.ApplicationManager1.ApplicationAdaptor -i ./dde-launchpad/toolGenerate/qdbusxml2cpp/org.desktopspec.ApplicationManager1.Application.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * This file may have been hand-edited. Look for HAND-EDIT comments
 * before re-generating it.
 */

#ifndef ORG_DESKTOPSPEC_APPLICATIONMANAGER1_APPLICATIONADAPTOR_H
#define ORG_DESKTOPSPEC_APPLICATIONMANAGER1_APPLICATIONADAPTOR_H

#include <QtCore/QObject>
#include <QtDBus/QtDBus>
#include "./dde-launchpad/toolGenerate/qdbusxml2cpp/org.desktopspec.ApplicationManager1.Application.h"
QT_BEGIN_NAMESPACE
class QByteArray;
template<class T> class QList;
template<class Key, class Value> class QMap;
class QString;
class QStringList;
class QVariant;
QT_END_NAMESPACE

/*
 * Adaptor class for interface org.desktopspec.ApplicationManager1.Application
 */
class ApplicationAdaptor: public QDBusAbstractAdaptor
{
    Q_OBJECT
    Q_CLASSINFO("D-Bus Interface", "org.desktopspec.ApplicationManager1.Application")
    Q_CLASSINFO("D-Bus Introspection", ""
"  <interface name=\"org.desktopspec.ApplicationManager1.Application\">\n"
"    <annotation value=\"This interface is designed to provide a dbus interface of desktop file. Missing fields will be added later.\" name=\"org.freedesktop.DBus.Description\"/>\n"
"    <property access=\"read\" type=\"as\" name=\"Categories\"/>\n"
"    <property access=\"read\" type=\"b\" name=\"X_linglong\"/>\n"
"    <property access=\"read\" type=\"b\" name=\"X_Flatpak\"/>\n"
"    <property access=\"read\" type=\"s\" name=\"X_Deepin_Vendor\">\n"
"      <annotation value=\"Whem this property is 'deepin', display name of the application                               should use GenericName.\" name=\"org.freedesktop.DBus.Description\"/>\n"
"    </property>\n"
"    <property access=\"read\" type=\"b\" name=\"NoDisplay\"/>\n"
"    <property access=\"readwrite\" type=\"as\" name=\"MimeTypes\"/>\n"
"    <property access=\"read\" type=\"as\" name=\"Actions\">\n"
"      <annotation value=\"Names of all action identifiers of this application.                        Check https://specifications.freedesktop.org/desktop-entry-spec/desktop-entry-spec-latest.html#extra-actions                        for futher information.\" name=\"org.freedesktop.DBus.Description\"/>\n"
"    </property>\n"
"    <property access=\"readwrite\" type=\"b\" name=\"AutoStart\"/>\n"
"    <property access=\"read\" type=\"x\" name=\"LastLaunchedTime\">\n"
"      <annotation value=\"Set the value of this property to -1 to                        indicates that some errors has occured.\" name=\"org.freedesktop.DBus.Description\"/>\n"
"    </property>\n"
"    <property access=\"read\" type=\"x\" name=\"LaunchedTimes\">\n"
"      <annotation value=\"Set the value of this property to -1 to                        indicates that some errors has occured.\" name=\"org.freedesktop.DBus.Description\"/>\n"
"    </property>\n"
"    <property access=\"read\" type=\"x\" name=\"InstalledTime\">\n"
"      <annotation value=\"Set the value of this property to -1 to                    indicates that some errors has occured.\" name=\"org.freedesktop.DBus.Description\"/>\n"
"    </property>\n"
"    <property access=\"read\" type=\"ao\" name=\"Instances\">\n"
"      <annotation value=\"All instances of this application.\" name=\"org.freedesktop.DBus.Description\"/>\n"
"    </property>\n"
"    <property access=\"read\" type=\"s\" name=\"ID\">\n"
"      <annotation value=\"The desktop file id of this application.                        Check https://specifications.freedesktop.org/desktop-entry-spec/desktop-entry-spec-latest.html#desktop-file-id                        for futher infomation.\" name=\"org.freedesktop.DBus.Description\"/>\n"
"    </property>\n"
"    <property access=\"read\" type=\"b\" name=\"Terminal\">\n"
"      <annotation value=\"Indicate this application should launch by terminal or not.\" name=\"org.freedesktop.DBus.Description\"/>\n"
"    </property>\n"
"    <property access=\"readwrite\" type=\"s\" name=\"Environ\">\n"
"      <annotation value=\"Indicate the environment of application's instance.                        passing some specific environment variables to Launch                        this application, eg. 'LANG=en_US;PATH=xxx:yyy;'\" name=\"org.freedesktop.DBus.Description\"/>\n"
"    </property>\n"
"    <property access=\"read\" type=\"a{sa{ss}}\" name=\"ActionName\">\n"
"      <annotation value=\"The type of ActionName is a Map, first key represents action, second key represents locale and the value is the corresponding content.\" name=\"org.freedesktop.DBus.Description\"/>\n"
"      <annotation value=\"PropMap\" name=\"org.qtproject.QtDBus.QtTypeName\"/>\n"
"    </property>\n"
"    <property access=\"read\" type=\"a{ss}\" name=\"Icons\">\n"
"      <annotation value=\"The type of IconName is a Map, where the key represents the action and the value is the corresponding content.\" name=\"org.freedesktop.DBus.Description\"/>\n"
"      <annotation value=\"QStringMap\" name=\"org.qtproject.QtDBus.QtTypeName\"/>\n"
"    </property>\n"
"    <property access=\"read\" type=\"a{ss}\" name=\"Name\">\n"
"      <annotation value=\"The meaning of this property's type is same as which in ActionName.\" name=\"org.freedesktop.DBus.Description\"/>\n"
"      <annotation value=\"QStringMap\" name=\"org.qtproject.QtDBus.QtTypeName\"/>\n"
"    </property>\n"
"    <property access=\"read\" type=\"a{ss}\" name=\"GenericName\">\n"
"      <annotation value=\"The meaning of this property's type is same as which in ActionName.\" name=\"org.freedesktop.DBus.Description\"/>\n"
"      <annotation value=\"QStringMap\" name=\"org.qtproject.QtDBus.QtTypeName\"/>\n"
"    </property>\n"
"    <method name=\"Launch\">\n"
"      <arg direction=\"in\" type=\"s\" name=\"action\"/>\n"
"      <arg direction=\"in\" type=\"as\" name=\"fields\"/>\n"
"      <arg direction=\"out\" type=\"o\" name=\"job\"/>\n"
"      <arg direction=\"in\" type=\"a{sv}\" name=\"options\"/>\n"
"      <annotation value=\"QVariantMap\" name=\"org.qtproject.QtDBus.QtTypeName.In2\"/>\n"
"      <annotation value=\"Given an action identifier,                        and some fields (file path or URI),                        this method will launch this application,                        and the object path of the job launching application.                        Result of that job is a object path                        of the new application instance.                        If that job failed, the result is a bool `false`.                         Extra options can be passed in `options` argument:                        1. `uid` (type u):                           The user id as who is that application will be run.                           This option might request a polikit authentication.                        2. `env` (type s):                           passing some specific environment variables to Launch                           this application, eg. 'LANG=en_US;PATH=xxx:yyy;'                        3. `path` (type s):                           set this application's working directory, please pass                           absolute directory path.                        NOTE:                        When application launched with `uid` option,                        `env` option will not take effect at all.\" name=\"org.freedesktop.DBus.Description\"/>\n"
"    </method>\n"
"    <property access=\"read\" type=\"b\" name=\"isOnDesktop\"/>\n"
"    <method name=\"SendToDesktop\">\n"
"      <arg direction=\"out\" type=\"b\" name=\"success\"/>\n"
"    </method>\n"
"    <method name=\"RemoveFromDesktop\">\n"
"      <arg direction=\"out\" type=\"b\" name=\"success\"/>\n"
"    </method>\n"
"  </interface>\n"
        "")
public:
    ApplicationAdaptor(QObject *parent);
    virtual ~ApplicationAdaptor();

public: // PROPERTIES
    Q_PROPERTY(PropMap ActionName READ actionName)
    PropMap actionName() const;

    Q_PROPERTY(QStringList Actions READ actions)
    QStringList actions() const;

    Q_PROPERTY(bool AutoStart READ autoStart WRITE setAutoStart)
    bool autoStart() const;
    void setAutoStart(bool value);

    Q_PROPERTY(QStringList Categories READ categories)
    QStringList categories() const;

    Q_PROPERTY(QString Environ READ environ WRITE setEnviron)
    QString environ() const;
    void setEnviron(const QString &value);

    Q_PROPERTY(QStringMap GenericName READ genericName)
    QStringMap genericName() const;

    Q_PROPERTY(QString ID READ iD)
    QString iD() const;

    Q_PROPERTY(QStringMap Icons READ icons)
    QStringMap icons() const;

    Q_PROPERTY(qlonglong InstalledTime READ installedTime)
    qlonglong installedTime() const;

    Q_PROPERTY(QList<QDBusObjectPath> Instances READ instances)
    QList<QDBusObjectPath> instances() const;

    Q_PROPERTY(qlonglong LastLaunchedTime READ lastLaunchedTime)
    qlonglong lastLaunchedTime() const;

    Q_PROPERTY(qlonglong LaunchedTimes READ launchedTimes)
    qlonglong launchedTimes() const;

    Q_PROPERTY(QStringList MimeTypes READ mimeTypes WRITE setMimeTypes)
    QStringList mimeTypes() const;
    void setMimeTypes(const QStringList &value);

    Q_PROPERTY(QStringMap Name READ name)
    QStringMap name() const;

    Q_PROPERTY(bool NoDisplay READ noDisplay)
    bool noDisplay() const;

    Q_PROPERTY(bool Terminal READ terminal)
    bool terminal() const;

    Q_PROPERTY(QString X_Deepin_Vendor READ x_Deepin_Vendor)
    QString x_Deepin_Vendor() const;

    Q_PROPERTY(bool X_Flatpak READ x_Flatpak)
    bool x_Flatpak() const;

    Q_PROPERTY(bool X_linglong READ x_linglong)
    bool x_linglong() const;

    Q_PROPERTY(bool isOnDesktop READ isOnDesktop)
    bool isOnDesktop() const;

public Q_SLOTS: // METHODS
    QDBusObjectPath Launch(const QString &action, const QStringList &fields, const QVariantMap &options);
    bool RemoveFromDesktop();
    bool SendToDesktop();
Q_SIGNALS: // SIGNALS
};

#endif
