/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-launchpad/dbus/org.deepin.dde.Launcher1.xml -a ./dde-launchpad/toolGenerate/qdbusxml2cpp/org.deepin.dde.Launcher1Adaptor -i ./dde-launchpad/toolGenerate/qdbusxml2cpp/org.deepin.dde.Launcher1.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * Do not edit! All changes made to it will be lost.
 */

#include "./dde-launchpad/toolGenerate/qdbusxml2cpp/org.deepin.dde.Launcher1Adaptor.h"
#include <QtCore/QMetaObject>
#include <QtCore/QByteArray>
#include <QtCore/QList>
#include <QtCore/QMap>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QVariant>

/*
 * Implementation of adaptor class Launcher1Adaptor
 */

Launcher1Adaptor::Launcher1Adaptor(QObject *parent)
    : QDBusAbstractAdaptor(parent)
{
    // constructor
    setAutoRelaySignals(true);
}

Launcher1Adaptor::~Launcher1Adaptor()
{
    // destructor
}

bool Launcher1Adaptor::visible() const
{
    // get the value of property Visible
    return qvariant_cast< bool >(parent()->property("Visible"));
}

void Launcher1Adaptor::Exit()
{
    // handle method call org.deepin.dde.Launcher1.Exit
    QMetaObject::invokeMethod(parent(), "Exit");
}

void Launcher1Adaptor::Hide()
{
    // handle method call org.deepin.dde.Launcher1.Hide
    QMetaObject::invokeMethod(parent(), "Hide");
}

void Launcher1Adaptor::Show()
{
    // handle method call org.deepin.dde.Launcher1.Show
    QMetaObject::invokeMethod(parent(), "Show");
}

void Launcher1Adaptor::ShowByMode(qlonglong in0)
{
    // handle method call org.deepin.dde.Launcher1.ShowByMode
    QMetaObject::invokeMethod(parent(), "ShowByMode", Q_ARG(qlonglong, in0));
}

void Launcher1Adaptor::Toggle()
{
    // handle method call org.deepin.dde.Launcher1.Toggle
    QMetaObject::invokeMethod(parent(), "Toggle");
}

