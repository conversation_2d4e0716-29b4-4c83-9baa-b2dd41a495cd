/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-launchpad/src/ddeintegration/xml/org.deepin.dde.daemon.Launcher1.xml -a ./dde-launchpad/toolGenerate/qdbusxml2cpp/org.deepin.dde.daemon.Launcher1Adaptor -i ./dde-launchpad/toolGenerate/qdbusxml2cpp/org.deepin.dde.daemon.Launcher1.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * Do not edit! All changes made to it will be lost.
 */

#include "./dde-launchpad/toolGenerate/qdbusxml2cpp/org.deepin.dde.daemon.Launcher1Adaptor.h"
#include <QtCore/QMetaObject>
#include <QtCore/QByteArray>
#include <QtCore/QList>
#include <QtCore/QMap>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QVariant>

/*
 * Implementation of adaptor class Launcher1Adaptor
 */

Launcher1Adaptor::Launcher1Adaptor(QObject *parent)
    : QDBusAbstractAdaptor(parent)
{
    // constructor
    setAutoRelaySignals(true);
}

Launcher1Adaptor::~Launcher1Adaptor()
{
    // destructor
}

void Launcher1Adaptor::RequestUninstall(const QString &desktop, bool unused)
{
    // handle method call org.deepin.dde.daemon.Launcher1.RequestUninstall
    QMetaObject::invokeMethod(parent(), "RequestUninstall", Q_ARG(QString, desktop), Q_ARG(bool, unused));
}

