/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-launchpad/src/ddeintegration/xml/org.desktopspec.ApplicationManager1.Application.xml -a ./dde-launchpad/toolGenerate/qdbusxml2cpp/org.desktopspec.ApplicationManager1.ApplicationAdaptor -i ./dde-launchpad/toolGenerate/qdbusxml2cpp/org.desktopspec.ApplicationManager1.Application.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * Do not edit! All changes made to it will be lost.
 */

#include "./dde-launchpad/toolGenerate/qdbusxml2cpp/org.desktopspec.ApplicationManager1.ApplicationAdaptor.h"
#include <QtCore/QMetaObject>
#include <QtCore/QByteArray>
#include <QtCore/QList>
#include <QtCore/QMap>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QVariant>

/*
 * Implementation of adaptor class ApplicationAdaptor
 */

ApplicationAdaptor::ApplicationAdaptor(QObject *parent)
    : QDBusAbstractAdaptor(parent)
{
    // constructor
    setAutoRelaySignals(true);
}

ApplicationAdaptor::~ApplicationAdaptor()
{
    // destructor
}

PropMap ApplicationAdaptor::actionName() const
{
    // get the value of property ActionName
    return qvariant_cast< PropMap >(parent()->property("ActionName"));
}

QStringList ApplicationAdaptor::actions() const
{
    // get the value of property Actions
    return qvariant_cast< QStringList >(parent()->property("Actions"));
}

bool ApplicationAdaptor::autoStart() const
{
    // get the value of property AutoStart
    return qvariant_cast< bool >(parent()->property("AutoStart"));
}

void ApplicationAdaptor::setAutoStart(bool value)
{
    // set the value of property AutoStart
    parent()->setProperty("AutoStart", QVariant::fromValue(value));
}

QStringList ApplicationAdaptor::categories() const
{
    // get the value of property Categories
    return qvariant_cast< QStringList >(parent()->property("Categories"));
}

QString ApplicationAdaptor::environ() const
{
    // get the value of property Environ
    return qvariant_cast< QString >(parent()->property("Environ"));
}

void ApplicationAdaptor::setEnviron(const QString &value)
{
    // set the value of property Environ
    parent()->setProperty("Environ", QVariant::fromValue(value));
}

QStringMap ApplicationAdaptor::genericName() const
{
    // get the value of property GenericName
    return qvariant_cast< QStringMap >(parent()->property("GenericName"));
}

QString ApplicationAdaptor::iD() const
{
    // get the value of property ID
    return qvariant_cast< QString >(parent()->property("ID"));
}

QStringMap ApplicationAdaptor::icons() const
{
    // get the value of property Icons
    return qvariant_cast< QStringMap >(parent()->property("Icons"));
}

qlonglong ApplicationAdaptor::installedTime() const
{
    // get the value of property InstalledTime
    return qvariant_cast< qlonglong >(parent()->property("InstalledTime"));
}

QList<QDBusObjectPath> ApplicationAdaptor::instances() const
{
    // get the value of property Instances
    return qvariant_cast< QList<QDBusObjectPath> >(parent()->property("Instances"));
}

qlonglong ApplicationAdaptor::lastLaunchedTime() const
{
    // get the value of property LastLaunchedTime
    return qvariant_cast< qlonglong >(parent()->property("LastLaunchedTime"));
}

qlonglong ApplicationAdaptor::launchedTimes() const
{
    // get the value of property LaunchedTimes
    return qvariant_cast< qlonglong >(parent()->property("LaunchedTimes"));
}

QStringList ApplicationAdaptor::mimeTypes() const
{
    // get the value of property MimeTypes
    return qvariant_cast< QStringList >(parent()->property("MimeTypes"));
}

void ApplicationAdaptor::setMimeTypes(const QStringList &value)
{
    // set the value of property MimeTypes
    parent()->setProperty("MimeTypes", QVariant::fromValue(value));
}

QStringMap ApplicationAdaptor::name() const
{
    // get the value of property Name
    return qvariant_cast< QStringMap >(parent()->property("Name"));
}

bool ApplicationAdaptor::noDisplay() const
{
    // get the value of property NoDisplay
    return qvariant_cast< bool >(parent()->property("NoDisplay"));
}

bool ApplicationAdaptor::terminal() const
{
    // get the value of property Terminal
    return qvariant_cast< bool >(parent()->property("Terminal"));
}

QString ApplicationAdaptor::x_Deepin_Vendor() const
{
    // get the value of property X_Deepin_Vendor
    return qvariant_cast< QString >(parent()->property("X_Deepin_Vendor"));
}

bool ApplicationAdaptor::x_Flatpak() const
{
    // get the value of property X_Flatpak
    return qvariant_cast< bool >(parent()->property("X_Flatpak"));
}

bool ApplicationAdaptor::x_linglong() const
{
    // get the value of property X_linglong
    return qvariant_cast< bool >(parent()->property("X_linglong"));
}

bool ApplicationAdaptor::isOnDesktop() const
{
    // get the value of property isOnDesktop
    return qvariant_cast< bool >(parent()->property("isOnDesktop"));
}

QDBusObjectPath ApplicationAdaptor::Launch(const QString &action, const QStringList &fields, const QVariantMap &options)
{
    // handle method call org.desktopspec.ApplicationManager1.Application.Launch
    QDBusObjectPath job;
    QMetaObject::invokeMethod(parent(), "Launch", Q_RETURN_ARG(QDBusObjectPath, job), Q_ARG(QString, action), Q_ARG(QStringList, fields), Q_ARG(QVariantMap, options));
    return job;
}

bool ApplicationAdaptor::RemoveFromDesktop()
{
    // handle method call org.desktopspec.ApplicationManager1.Application.RemoveFromDesktop
    bool success;
    QMetaObject::invokeMethod(parent(), "RemoveFromDesktop", Q_RETURN_ARG(bool, success));
    return success;
}

bool ApplicationAdaptor::SendToDesktop()
{
    // handle method call org.desktopspec.ApplicationManager1.Application.SendToDesktop
    bool success;
    QMetaObject::invokeMethod(parent(), "SendToDesktop", Q_RETURN_ARG(bool, success));
    return success;
}

