/**
 * This file is generated by dconfig2cpp.
 * Command line arguments: ./dconfig2cpp -p ./dde-launchpad/toolGenerate/dconfig2cpp ./dde-launchpad/src/models/org.deepin.ds.launchpad.json
 * Generation time: 2025-01-14T10:54:59
 * JSON file version: 1.0
 * 
 * WARNING: DO NOT MODIFY THIS FILE MANUALLY.
 * If you need to change the content, please modify the dconfig2cpp tool.
 */

#ifndef ORG_DEEPIN_DS_LAUNCHPAD_H
#define ORG_DEEPIN_DS_LAUNCHPAD_H

#include <QThread>
#include <QVariant>
#include <QDebug>
#include <QAtomicPointer>
#include <QAtomicInteger>
#include <DConfig>

class org_deepin_ds_launchpad : public QObject {
    Q_OBJECT

    Q_PROPERTY(double categoryType READ categoryType WRITE setCategoryType NOTIFY categoryTypeChanged)
    Q_PROPERTY(QList<QVariant> compulsoryAppIdList READ compulsoryAppIdList WRITE setCompulsoryAppIdList NOTIFY compulsoryAppIdListChanged)
    Q_PROPERTY(QList<QVariant> excludeAppIdList READ excludeAppIdList WRITE setExcludeAppIdList NOTIFY excludeAppIdListChanged)
    Q_PROPERTY(QList<QVariant> frequentlyUsedAppIdList READ frequentlyUsedAppIdList WRITE setFrequentlyUsedAppIdList NOTIFY frequentlyUsedAppIdListChanged)
public:
    explicit org_deepin_ds_launchpad(QThread *thread, const QString &appId, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(appId, name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    explicit org_deepin_ds_launchpad(QThread *thread, DTK_CORE_NAMESPACE::DConfigBackend *backend, const QString &appId, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(backend, appId, name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    explicit org_deepin_ds_launchpad(QThread *thread, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    explicit org_deepin_ds_launchpad(QThread *thread, DTK_CORE_NAMESPACE::DConfigBackend *backend, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(backend, name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    ~org_deepin_ds_launchpad() {
        if (m_config.loadRelaxed()) {
            m_config.loadRelaxed()->deleteLater();
        }
    }

    double categoryType() const {
        return p_categoryType;
    }
    void setCategoryType(const double &value) {
        auto oldValue = p_categoryType;
        p_categoryType = value;
        markPropertySet(0);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("categoryType"), value);
            });
        }
        if (p_categoryType != oldValue) {
            Q_EMIT categoryTypeChanged();
        }
    }
    QList<QVariant> compulsoryAppIdList() const {
        return p_compulsoryAppIdList;
    }
    void setCompulsoryAppIdList(const QList<QVariant> &value) {
        auto oldValue = p_compulsoryAppIdList;
        p_compulsoryAppIdList = value;
        markPropertySet(1);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("compulsoryAppIdList"), value);
            });
        }
        if (p_compulsoryAppIdList != oldValue) {
            Q_EMIT compulsoryAppIdListChanged();
        }
    }
    QList<QVariant> excludeAppIdList() const {
        return p_excludeAppIdList;
    }
    void setExcludeAppIdList(const QList<QVariant> &value) {
        auto oldValue = p_excludeAppIdList;
        p_excludeAppIdList = value;
        markPropertySet(2);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("excludeAppIdList"), value);
            });
        }
        if (p_excludeAppIdList != oldValue) {
            Q_EMIT excludeAppIdListChanged();
        }
    }
    QList<QVariant> frequentlyUsedAppIdList() const {
        return p_frequentlyUsedAppIdList;
    }
    void setFrequentlyUsedAppIdList(const QList<QVariant> &value) {
        auto oldValue = p_frequentlyUsedAppIdList;
        p_frequentlyUsedAppIdList = value;
        markPropertySet(3);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("frequentlyUsedAppIdList"), value);
            });
        }
        if (p_frequentlyUsedAppIdList != oldValue) {
            Q_EMIT frequentlyUsedAppIdListChanged();
        }
    }
Q_SIGNALS:
    void categoryTypeChanged();
    void compulsoryAppIdListChanged();
    void excludeAppIdListChanged();
    void frequentlyUsedAppIdListChanged();
private:
    void initialize(DTK_CORE_NAMESPACE::DConfig *config) {
        Q_ASSERT(!m_config.loadRelaxed());
        m_config.storeRelaxed(config);
        if (testPropertySet(0)) {
            config->setValue(QStringLiteral("categoryType"), QVariant::fromValue(p_categoryType));
        } else {
            updateValue(QStringLiteral("categoryType"), QVariant::fromValue(p_categoryType));
        }
        if (testPropertySet(1)) {
            config->setValue(QStringLiteral("compulsoryAppIdList"), QVariant::fromValue(p_compulsoryAppIdList));
        } else {
            updateValue(QStringLiteral("compulsoryAppIdList"), QVariant::fromValue(p_compulsoryAppIdList));
        }
        if (testPropertySet(2)) {
            config->setValue(QStringLiteral("excludeAppIdList"), QVariant::fromValue(p_excludeAppIdList));
        } else {
            updateValue(QStringLiteral("excludeAppIdList"), QVariant::fromValue(p_excludeAppIdList));
        }
        if (testPropertySet(3)) {
            config->setValue(QStringLiteral("frequentlyUsedAppIdList"), QVariant::fromValue(p_frequentlyUsedAppIdList));
        } else {
            updateValue(QStringLiteral("frequentlyUsedAppIdList"), QVariant::fromValue(p_frequentlyUsedAppIdList));
        }

        connect(config, &DTK_CORE_NAMESPACE::DConfig::valueChanged, this, [this](const QString &key) {
            updateValue(key);
        }, Qt::DirectConnection);
    }
    void updateValue(const QString &key, const QVariant &fallback = QVariant()) {
        Q_ASSERT(QThread::currentThread() == m_config.loadRelaxed()->thread());
        const QVariant &value = m_config.loadRelaxed()->value(key, fallback);
        if (key == QStringLiteral("categoryType")) {
            auto newValue = qvariant_cast<double>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_categoryType != newValue) {
                    p_categoryType = newValue;
                    Q_EMIT categoryTypeChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("compulsoryAppIdList")) {
            auto newValue = qvariant_cast<QList<QVariant>>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_compulsoryAppIdList != newValue) {
                    p_compulsoryAppIdList = newValue;
                    Q_EMIT compulsoryAppIdListChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("excludeAppIdList")) {
            auto newValue = qvariant_cast<QList<QVariant>>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_excludeAppIdList != newValue) {
                    p_excludeAppIdList = newValue;
                    Q_EMIT excludeAppIdListChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("frequentlyUsedAppIdList")) {
            auto newValue = qvariant_cast<QList<QVariant>>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_frequentlyUsedAppIdList != newValue) {
                    p_frequentlyUsedAppIdList = newValue;
                    Q_EMIT frequentlyUsedAppIdListChanged();
                }
            });
            return;
        }
    }
    inline void markPropertySet(const int index) {
        if (index < 32) {
            m_propertySetStatus0.fetchAndOrOrdered(1 << (index - 0));
            return;
        }
        Q_UNREACHABLE();
    }
    inline bool testPropertySet(const int index) const {
        if (index < 32) {
            return (m_propertySetStatus0.loadRelaxed() & (1 << (index - 0)));
        }
        Q_UNREACHABLE();
    }
    QAtomicPointer<DTK_CORE_NAMESPACE::DConfig> m_config = nullptr;
    double p_categoryType { 2 };
    QList<QVariant> p_compulsoryAppIdList { QList<QVariant>{QVariant(QStringLiteral("dde-computer.desktop")), QVariant(QStringLiteral("dde-trash.desktop")), QVariant(QStringLiteral("dde-file-manager.desktop")), QVariant(QStringLiteral("deepin-terminal.desktop")), QVariant(QStringLiteral("deepin-manual.desktop")), QVariant(QStringLiteral("deepin-system-monitor.desktop")), QVariant(QStringLiteral("deepin-devicemanager.desktop")), QVariant(QStringLiteral("dde-printer.desktop")), QVariant(QStringLiteral("dde-calendar.desktop")), QVariant(QStringLiteral("org.fcitx.fcitx5-migrator.desktop")), QVariant(QStringLiteral("fcitx5-configtool.desktop")), QVariant(QStringLiteral("org.fcitx.Fcitx5.desktop"))} };
    QList<QVariant> p_excludeAppIdList { QList<QVariant>{} };
    QList<QVariant> p_frequentlyUsedAppIdList { QList<QVariant>{QVariant(QStringLiteral("org.deepin.browser.desktop")), QVariant(QStringLiteral("deepin-app-store.desktop")), QVariant(QStringLiteral("dde-file-manager.desktop")), QVariant(QStringLiteral("deepin-mail.desktop")), QVariant(QStringLiteral("deepin-home.desktop")), QVariant(QStringLiteral("deepin-screen-recorder.desktop")), QVariant(QStringLiteral("deepin-calculator.desktop")), QVariant(QStringLiteral("deepin-terminal.desktop")), QVariant(QStringLiteral("dde-calendar.desktop")), QVariant(QStringLiteral("deepin-image-viewer.desktop")), QVariant(QStringLiteral("deepin-movie.desktop")), QVariant(QStringLiteral("deepin-album.desktop")), QVariant(QStringLiteral("deepin-system-monitor.desktop")), QVariant(QStringLiteral("libreoffice-base.desktop")), QVariant(QStringLiteral("libreoffice-writer.desktop")), QVariant(QStringLiteral("libreoffice-calc.desktop"))} };
    QAtomicInteger<quint32> m_propertySetStatus0 = 0;
};

#endif // ORG_DEEPIN_DS_LAUNCHPAD_H
