<interface name="org.deepin.dde.daemon.Launcher1">
  <method name="RequestUninstall">
    <arg direction="in" type="s" name="desktop"/>
    <arg direction="in" type="b" name="skipPreinstallHook"/>
  </method>
  <signal name="UninstallSuccess">
    <arg type="s" name="appID"/>
  </signal>
  <signal name="UninstallFailed">
     <arg type="s" name="appId"/>
     <arg type="s" name="errMsg"/>
  </signal>
</interface>

