// SPDX-FileCopyrightText: 2023 UnionTech Software Technology Co., Ltd.
//
// SPDX-License-Identifier: GPL-3.0-or-later

#include "iconutils.h"

#include <QDate>
#include <QDebug>
#include <QIcon>
#include <QStandardPaths>
#include <QGuiApplication>
#include <QFile>
#include <DIcon>
#include <DIconTheme>
#include <DSvgRenderer>
#include <QFileInfo>
#include <QPainter>

DGUI_USE_NAMESPACE

bool IconUtils::getThemeIcon(QPixmap &pixmap, const QString &iconName, const int size)
{
    QString actualIconName;
    QIcon icon;
    bool findIcon = true;

    if (iconName == "dde-calendar-dynamic") {
        QString name = QStandardPaths::standardLocations(QStandardPaths::TempLocation).constFirst() + "/"
                       +  QString::number(QDate::currentDate().year())
                       + "_" + QString::number(QDate::currentDate().dayOfYear()) + ".svg";

        if (!createCalendarIcon(name))
            actualIconName = iconName;
        else
            actualIconName = name;
    } else {
        actualIconName = iconName;
    }

    const qreal ratio = qApp->devicePixelRatio();
    const int iconSize = perfectIconSize(size);

    do {
        if (actualIconName.isEmpty())
            return findIcon;

        if (actualIconName.startsWith("data:image/")) {
            const QStringList strs = actualIconName.split("base64,");
            if (strs.size() == 2)
                pixmap.loadFromData(QByteArray::fromBase64(strs.at(1).toLatin1()));

            if (!pixmap.isNull())
                break;
        }

        if (QFile::exists(actualIconName)) {
            if (actualIconName.endsWith(".svg"))
                pixmap = loadSvg(iconName, qRound(iconSize * ratio));
            else
                pixmap = DIcon::loadNxPixmap(actualIconName);

            if (!pixmap.isNull())
                break;
        }

        icon = DIconTheme::findQIcon(actualIconName);

        // TODO why icon is not null, but it's name is empty.
        if (icon.isNull() || icon.name().isEmpty()) {
            icon = QIcon(":/images/application-x-desktop.svg");
            qWarning() << "It fallbacks to default icon for [" << actualIconName << "].";
            findIcon = false;
        }

        pixmap = icon.pixmap(QSize(iconSize, iconSize) * ratio);
        if (!pixmap.isNull())
            break;

    } while (false);

    pixmap = pixmap.scaled(QSize(iconSize, iconSize) * ratio, Qt::KeepAspectRatio, Qt::SmoothTransformation);
    pixmap.setDevicePixelRatio(ratio);

    return findIcon;
}

static const QVector<int> sizes { 16, 18, 24, 32, 64, 96, 128, 256 };
int IconUtils::perfectIconSize(const int size)
{
    auto iter = std::lower_bound(sizes.begin(), sizes.end(), size);
    if (iter != sizes.end())
        return *iter;
    return sizes[0];
}

int IconUtils::perfectIconSizeReverse(const int size)
{
    auto iter = std::lower_bound(sizes.rbegin(), sizes.rend(), size, std::greater<int>());
    if (iter != sizes.rend())
        return *iter;
    return sizes[0];
}

bool IconUtils::createCalendarIcon(const QString &fileName)
{
    static const QByteArrayList &dayList= {  "<polygon id=\"path-5\" points=\"50.2401631 67.0997814 50.2401631 37.9113049 46.0560811 37.9113049 41.9120381 40.9342541 41.9120381 45.37859 46.0560811 42.3356213 46.0560811 67.0997814\"></polygon>\n"
        , "<path d=\"M56.0328614,66.8683044 L56.0328614,62.6842223 L44.8819825,62.6842223 L54.2711427,50.912738 C55.4456218,49.4179463 56.0328614,47.709613 56.0328614,45.787738 C56.0061687,43.3853942 55.1987143,41.4034606 53.6104981,39.8419372 C52.0489747,38.2670674 50.0136557,37.4662861 47.5045411,37.4395934 C45.2623536,37.4662861 43.3738445,38.253721 41.8390138,39.8018981 C40.3175294,41.3901143 39.5034018,43.3987406 39.3966309,45.827777 L43.5606934,45.827777 C43.7075033,44.4931416 44.1746257,43.4587992 44.9620606,42.7247497 C45.7228028,41.9907002 46.6770671,41.6236755 47.8248536,41.6236755 C49.11945,41.6503682 50.1204265,42.0707783 50.8277833,42.8849059 C51.5084473,43.6990335 51.8487794,44.6532979 51.8487794,45.7476989 C51.8487794,46.1614359 51.795394,46.6018656 51.6886231,47.068988 C51.5284669,47.5628031 51.2281739,48.0966572 50.7877442,48.6705505 L39.3966309,62.9244567 L39.3966309,66.8683044 L56.0328614,66.8683044 Z\" id=\"path-5\"></path>\n"
        , "<path d=\"M46.9058157,67.1812035 C49.481662,67.1545108 51.590386,66.3337101 53.2319875,64.7188012 C54.9002818,63.1439314 55.7477753,60.9884952 55.774468,58.2524926 C55.774468,57.024628 55.5142141,55.8501489 54.9937063,54.7290551 C54.4465058,53.6079614 53.5589732,52.6470239 52.3311086,51.8462426 C53.5322805,51.032115 54.3664276,50.1045434 54.83355,49.0635278 C55.2339407,48.0225121 55.434136,46.9414575 55.434136,45.8203637 C55.4074433,43.6048689 54.6467011,41.6829939 53.1519094,40.0547387 C51.590386,38.3864444 49.481662,37.5389509 46.8257375,37.5122582 C44.7303599,37.5389509 42.9286021,38.2863468 41.4204641,39.7544457 C39.8856334,41.235891 39.0114472,43.0576684 38.7979055,45.2197778 L42.9819875,45.2197778 C43.2489146,44.0186059 43.7694224,43.1310733 44.543511,42.5571801 C45.2775605,41.9832869 46.0916881,41.6963403 46.9858938,41.6963403 C48.2271047,41.723033 49.2414276,42.1167504 50.0288625,42.8774926 C50.8162974,43.6649275 51.2233612,44.6725772 51.250054,45.9004418 C51.250054,47.1149601 50.8563365,48.1025903 50.0689016,48.8633325 C49.2814667,49.6507674 48.1737193,50.0444848 46.7456594,50.0444848 L45.3042532,50.0444848 L45.3042532,53.7480981 L47.0659719,53.7480981 C48.3605683,53.7480981 49.4282766,54.1418155 50.2690969,54.9292504 C51.1232636,55.743378 51.5636933,56.8911645 51.590386,58.3726098 C51.5636933,59.8273624 51.1232636,60.9484562 50.2690969,61.735891 C49.4282766,62.5767114 48.3939342,62.9971215 47.1660696,62.9971215 C46.0182831,62.9971215 45.0973847,62.6834822 44.4033743,62.0562035 C43.7227102,61.4556176 43.1955292,60.6681827 42.8218313,59.6938989 L38.6377493,59.6938989 C39.0915253,62.1229353 40.0858287,63.9780785 41.6206594,65.2593285 C43.1554901,66.5405785 44.9172089,67.1812035 46.9058157,67.1812035 Z\" id=\"path-5\"></path>\n"
        , "<polygon id=\"path-5\" points=\"54.946556 66.8392736 54.946556 62.4950353 57.2688216 62.4950353 57.2688216 58.5511877 54.946556 58.5511877 54.946556 50.1630041 50.762474 50.1630041 50.762474 58.5511877 43.1350325 58.5511877 53.0046615 37.650797 48.3401107 37.650797 38.590599 58.5511877 38.590599 62.4950353 50.762474 62.4950353 50.762474 66.8392736\"></polygon>\n"
        , "<path d=\"M48.1566324,67.078339 C49.6247314,67.0516463 50.892635,66.7113143 51.9603434,66.0573429 C53.0547444,65.4300643 53.8888916,64.7360538 54.4627848,63.9753117 C55.1167561,63.2012231 55.5838785,62.3136906 55.864152,61.312714 C56.1310791,60.2983911 56.2645426,58.8436385 56.2645426,56.9484562 C56.2645426,55.7205916 56.2245035,54.6862492 56.1444254,53.8454288 C56.0643473,53.0179549 55.9308837,52.3172713 55.7440348,51.7433781 C55.3836832,50.6890161 54.7964436,49.7614445 53.982316,48.9606632 C53.4351155,48.3600773 52.7210856,47.8529158 51.8402262,47.4391788 C50.9059814,47.0654809 49.8783121,46.8652856 48.7572184,46.8385929 C47.008846,46.8385929 45.4539957,47.3724471 44.0926676,48.4401554 L44.0926676,41.8337101 L55.7039957,41.8337101 L55.7039957,37.6496281 L40.1488199,37.6496281 L40.1488199,53.4250187 L44.0926676,53.4250187 C44.5864827,52.530813 45.1470296,51.9035343 45.7743082,51.5431827 C46.4015869,51.1961775 47.0955973,51.0226749 47.8563395,51.0226749 C48.7105061,51.0226749 49.4245361,51.1427921 49.9984293,51.3830265 C50.5723225,51.6766463 51.0060791,52.0703637 51.2996988,52.5641788 C51.8468994,53.6051945 52.1071533,54.9598494 52.0804606,56.6281437 C52.0804606,57.3088077 52.0671142,58.0094913 52.0404215,58.7301945 C51.9870361,59.4642439 51.8402262,60.144908 51.5999918,60.7721867 C51.3731038,61.3994653 50.9727132,61.9066268 50.3988199,62.293671 C49.798234,62.6940617 48.9974528,62.894257 47.9964762,62.894257 C45.6608642,62.8675643 44.2661702,61.7331242 43.8123942,59.4909367 L39.6283121,59.4909367 C40.0287028,62.1735538 41.0630452,64.1154484 42.7313395,65.3166202 C44.372941,66.4910994 46.181372,67.078339 48.1566324,67.078339 Z\" id=\"path-5\"></path>\n"
        , "<path d=\"M45.4130711,66.7936821 C46.3473159,67.0606091 47.4216974,67.1406873 48.6362156,67.0339164 C49.7973485,66.940492 50.798325,66.6935844 51.6391453,66.2931938 C52.4799656,65.8928032 53.1939956,65.415671 53.7812352,64.8617973 C54.3684748,64.3079236 54.8422703,63.720684 55.2026219,63.1000785 C55.5629735,62.4794731 55.8399103,61.8989067 56.0334324,61.3583793 C56.2269546,60.817852 56.3604181,60.3507296 56.4338231,59.9570121 C56.507228,59.5632947 56.5506037,59.3197237 56.56395,59.2262992 C56.5772964,59.0394503 56.5873061,58.8125623 56.5939793,58.5456352 C56.6006525,58.2787081 56.6039891,58.0151176 56.6039891,57.7548637 L56.6039891,57.7548637 L56.6039891,56.6037407 C56.6039891,56.3501599 56.5672866,55.9998181 56.4938817,55.5527153 C56.4204767,55.1056124 56.2903498,54.6251436 56.1035008,54.111309 C55.9166518,53.5974744 55.6597345,53.0702934 55.3327488,52.529766 C55.0057632,51.9892387 54.5953628,51.4987602 54.1015477,51.0583305 C53.6077326,50.6179008 53.0171564,50.2508761 52.3298192,49.9572563 C51.6424819,49.6636365 50.8383641,49.5034802 49.9174656,49.4767875 C49.2768406,49.4500948 48.7129572,49.4601046 48.2258153,49.5068168 C47.7386733,49.5535291 47.3015802,49.6903292 46.914536,49.9172172 C47.1013849,49.5969047 47.3349461,49.1698214 47.6152195,48.6359672 C47.895493,48.1021131 48.2057957,47.5082003 48.5461278,46.8542289 C48.8864598,46.2002576 49.2401382,45.5129203 49.6071629,44.7922172 C49.9741876,44.0715141 50.3345392,43.3574841 50.6882176,42.6501274 L50.6882176,42.6501274 L51.206223,41.6141166 C51.3747207,41.2771212 51.5390477,40.9484672 51.6992039,40.6281547 C52.0195164,39.9875297 52.2997899,39.4203097 52.5400242,38.9264946 C52.7802586,38.4326795 52.9704442,38.0422986 53.1105809,37.755352 C53.2507176,37.4684054 53.3141128,37.3249321 53.3007664,37.3249321 L53.3007664,37.3249321 L48.576157,37.3249321 C47.2815607,39.9274711 46.1137547,42.2831026 45.0727391,44.3918266 C44.6323094,45.2993787 44.1918797,46.1935844 43.75145,47.0744438 C43.3110203,47.9553032 42.9139663,48.7627576 42.5602879,49.4968071 C42.2066095,50.2308565 41.9096531,50.8481254 41.6694188,51.3486137 C41.4291844,51.849102 41.2823745,52.1660779 41.2289891,52.2995414 C41.0154474,52.8333956 40.8285985,53.3272107 40.6684422,53.7809867 C40.508286,54.2347628 40.3648126,54.695212 40.2380223,55.1623344 C40.1112319,55.6294568 40.0011245,56.1199353 39.9077,56.63377 C39.8142755,57.1476046 39.7275242,57.7315076 39.6474461,58.3854789 C39.5940607,58.8259086 39.6107436,59.3597628 39.6974949,59.9870414 C39.7842462,60.6143201 39.9577488,61.2682914 40.2180028,61.9489555 C40.4782567,62.6296196 40.8352716,63.2936007 41.2890477,63.9408989 C41.7428237,64.588197 42.3100438,65.1587537 42.9907078,65.6525688 C43.6713719,66.1463839 44.4788263,66.526755 45.4130711,66.7936821 Z M48.468501,62.749941 L48.2158055,62.7497367 C47.4684097,62.7497367 46.8444676,62.6563123 46.3439793,62.4694633 C45.843491,62.2826144 45.4330906,62.04238 45.1127781,61.7487602 C44.7924656,61.4551404 44.5455581,61.118145 44.3720555,60.7377739 C44.1985529,60.3574028 44.0750991,59.9636853 44.0016942,59.5566215 C43.9282892,59.1495577 43.8849136,58.7424939 43.8715672,58.3354301 C43.8582208,57.9283663 43.8515477,57.558005 43.8515477,57.2243461 C43.8515477,57.0775362 43.8849136,56.8673311 43.9516453,56.5937309 C44.0183771,56.3201306 44.1284845,56.0231742 44.2819676,55.7028617 C44.4354507,55.3825492 44.6289728,55.0589002 44.862534,54.7319145 C45.0960952,54.4049288 45.373032,54.1146456 45.6933445,53.8610649 C46.013657,53.6074841 46.3806818,53.4072888 46.7944188,53.2604789 C47.2081557,53.113669 47.668605,53.0602836 48.1757664,53.1003227 C49.2568211,53.1804008 50.0909682,53.4206352 50.6782078,53.8210258 C51.2654474,54.2214164 51.6891942,54.6985486 51.9494481,55.2524223 C52.209702,55.806296 52.3598485,56.390199 52.3998875,57.0041313 C52.4399266,57.6180636 52.4599461,58.1786104 52.4599461,58.6857719 C52.4599461,58.8058891 52.4399266,58.9894015 52.3998875,59.236309 C52.3598485,59.4832166 52.2831069,59.7601534 52.1696629,60.0671196 C52.0562189,60.3740857 51.8993992,60.687725 51.6992039,61.0080375 C51.4990086,61.32835 51.2420913,61.6186332 50.928452,61.8788871 C50.6148126,62.139141 50.2344416,62.3526827 49.7873387,62.5195121 C49.3402358,62.6863416 48.8163914,62.7630831 48.2158055,62.7497367 L48.468501,62.749941 Z\" id=\"path-5\"></path>\n"
        , "<polygon id=\"path-5\" points=\"46.8933466 66.7626536 56.7829951 41.7582591 56.7829951 37.5741771 40.1667841 37.5741771 40.1667841 45.9823802 44.3508662 45.9823802 44.3508662 41.7582591 52.0984248 41.7582591 42.2287959 66.7626536\"></polygon>\n"
        , "<path d=\"M47.9197005,67.079508 C50.4154687,67.0528153 52.5241927,66.2320145 54.2458724,64.6171056 C55.9408594,63.0422358 56.8016992,60.9401851 56.8283919,58.3109533 C56.8016992,55.4948726 55.6539128,53.3060705 53.3850325,51.744547 C54.2525456,50.9838048 54.9599023,50.1096186 55.5071029,49.1219884 C56.0276107,48.20109 56.2878646,47.106689 56.2878646,45.8387853 C56.2611719,43.4097489 55.4670638,41.4011226 53.9055404,39.8129064 C52.3573633,38.2380366 50.3620833,37.4372554 47.9197005,37.4105627 C45.5173568,37.4372554 43.5554427,38.2380366 42.0339583,39.8129064 C40.4190495,41.4011226 39.5982487,43.4097489 39.571556,45.8387853 C39.571556,47.106689 39.8585026,48.20109 40.4323958,49.1219884 C40.9262109,50.1096186 41.606875,50.9838048 42.474388,51.744547 C40.2055078,53.3060705 39.0577214,55.4948726 39.0310286,58.3109533 C39.0577214,60.9401851 39.9452539,63.0422358 41.6936263,64.6171056 C43.3619206,66.2320145 45.4372786,67.0528153 47.9197005,67.079508 Z M47.9197005,49.9427892 C46.8386458,49.9427892 45.8843815,49.5757645 45.0569075,48.841715 C44.2160872,48.1343582 43.7823307,47.106689 43.755638,45.7587072 C43.7823307,44.3706864 44.2160872,43.3229976 45.0569075,42.6156408 C45.8843815,41.9349767 46.8386458,41.5946447 47.9197005,41.5946447 C49.0407943,41.5946447 50.0150781,41.9349767 50.8425521,42.6156408 C51.6566797,43.3229976 52.0770898,44.3706864 52.1037825,45.7587072 C52.0770898,47.106689 51.6566797,48.1343582 50.8425521,48.841715 C50.0150781,49.5757645 49.0407943,49.9427892 47.9197005,49.9427892 Z M47.9197005,62.8954259 C46.6384505,62.8954259 45.5507227,62.4483231 44.6565169,61.5541173 C43.7222721,60.6999507 43.2418034,59.6055496 43.2151107,58.2709142 C43.2418034,56.9095861 43.7222721,55.8018387 44.6565169,54.947672 C45.5507227,54.1068517 46.6384505,53.6730952 47.9197005,53.6464025 C49.2409896,53.6730952 50.348737,54.1068517 51.2429427,54.947672 C52.1504948,55.8018387 52.6176172,56.9095861 52.6443099,58.2709142 C52.6176172,59.6055496 52.1504948,60.6999507 51.2429427,61.5541173 C50.348737,62.4483231 49.2409896,62.8954259 47.9197005,62.8954259 Z\" id=\"path-5\"></path>\n"
        , "<path d=\"M47.1906878,66.8880113 C48.4585914,64.3255113 49.6063779,62.0032457 50.6340471,59.9212144 C51.0744768,59.040355 51.5048967,58.1628323 51.9253069,57.2886461 C52.3457171,56.4144599 52.7327613,55.6203518 53.0864397,54.9063218 C53.4401181,54.1922919 53.7337379,53.5883694 53.9672991,53.0945543 C54.2008603,52.6007392 54.3443336,52.2804267 54.397719,52.1336168 C54.6112607,51.5997626 54.7981096,51.1059475 54.9582659,50.6521714 C55.1184221,50.1983954 55.2618954,49.7379462 55.3886858,49.2708238 C55.5154762,48.8037014 55.6255836,48.3132229 55.7190081,47.7993882 C55.8124326,47.2855536 55.8991839,46.7016506 55.979262,46.0476793 C56.0326474,45.6072496 56.019301,45.076732 55.9392229,44.4561265 C55.8591448,43.8355211 55.695652,43.1948961 55.4487444,42.5342515 C55.2018369,41.873607 54.8581682,41.2229722 54.4177385,40.5823472 C53.9773089,39.9417222 53.4234352,39.3811754 52.7561175,38.9007066 C52.0887997,38.4202379 51.2913551,38.0532131 50.3637835,37.7996324 C49.4362119,37.5460517 48.3651669,37.4726467 47.1506487,37.5794175 C45.9895159,37.6861884 44.9852027,37.9330959 44.1377092,38.3201402 C43.2902158,38.7071845 42.5761858,39.1676337 41.9956194,39.7014879 C41.415053,40.235342 40.944594,40.8058987 40.5842425,41.4131578 C40.2238909,42.0204169 39.9436175,42.5909735 39.7434221,43.1248277 C39.5432268,43.6586819 39.4064267,44.1157945 39.3330217,44.4961656 C39.2596168,44.8765367 39.2162412,45.1134345 39.2028948,45.2068589 C39.1762021,45.3937079 39.1595191,45.6205959 39.152846,45.887523 C39.1461728,46.1544501 39.1428362,46.4180406 39.1428362,46.6782945 C39.1428362,46.9385484 39.1461728,47.1754462 39.152846,47.3889879 C39.1595191,47.6025295 39.1628557,47.7493394 39.1628557,47.8294175 C39.1628557,48.0829983 39.2062314,48.4333401 39.2929827,48.8804429 C39.379734,49.3275458 39.5198707,49.8080145 39.7133928,50.3218492 C39.906915,50.8356838 40.1638323,51.3628648 40.4841448,51.9033921 C40.8044573,52.4439195 41.2048479,52.934398 41.6853167,53.3748277 C42.1657854,53.8152574 42.7363421,54.1822821 43.3969866,54.4759019 C44.0576311,54.7695217 44.8283831,54.929678 45.7092425,54.9563707 C46.3498675,54.9830634 46.9137509,54.9730536 47.4008928,54.9263414 C47.8880348,54.8796291 48.3251279,54.742829 48.7121721,54.515941 C48.4585914,54.9563707 48.1282691,55.5636298 47.7212053,56.3377183 C47.3141415,57.1118069 46.8770484,57.9593004 46.409926,58.8801988 C45.9428037,59.8010972 45.4690081,60.735342 44.9885393,61.6829332 C44.5080706,62.6305243 44.0743141,63.4913642 43.6872698,64.2654527 C43.3002255,65.0395412 42.9865862,65.6701565 42.7463518,66.1572984 C42.5061175,66.6444403 42.3926734,66.8880113 42.4060198,66.8880113 L42.4060198,66.8880113 L47.1906878,66.8880113 Z M47.7006297,51.3387672 L47.4509417,51.3328355 C46.4366188,51.2794501 45.6491839,51.0558987 45.088637,50.6621812 C44.5280901,50.2684638 44.1176897,49.7946682 43.8574358,49.2407945 C43.5971819,48.6869208 43.4403622,48.0963446 43.3869768,47.469066 C43.3335914,46.8417873 43.3068987,46.2678941 43.3068987,45.7473863 C43.3068987,45.58723 43.3669573,45.2936102 43.4870745,44.8665269 C43.6071917,44.4394436 43.8207333,43.9990139 44.1276995,43.5452379 C44.4346656,43.0914618 44.8517392,42.6777248 45.3789202,42.3040269 C45.9061012,41.930329 46.5834287,41.7234605 47.4109026,41.6834214 C48.1582984,41.6433824 48.7822405,41.7067776 49.2827288,41.873607 C49.7832171,42.0404364 50.1936175,42.270661 50.51393,42.5642808 C50.8342425,42.8579006 51.08115,43.2015692 51.2546526,43.5952867 C51.4281552,43.9890041 51.551609,44.3994045 51.6250139,44.8264879 C51.6984189,45.2535712 51.7384579,45.6739813 51.7451311,46.0877183 C51.7518043,46.5014553 51.7618141,46.8751532 51.7751604,47.2088121 C51.7751604,47.3689683 51.7417945,47.58251 51.6750628,47.8494371 C51.608331,48.1163642 51.4982236,48.4066474 51.3447405,48.7202867 C51.1912574,49.033926 50.9977353,49.3509019 50.7641741,49.6712144 C50.5306129,49.9915269 50.253676,50.2784735 49.9333635,50.5320543 C49.613051,50.785635 49.2460263,50.9891669 48.8322893,51.14265 C48.4185523,51.296133 47.9581031,51.3595282 47.4509417,51.3328355 L47.7006297,51.3387672 Z\" id=\"path-5\"></path>\n"
        , "<path d=\"M39.6710307,66.3993207 L39.6710307,37.2108442 L35.4869487,37.2108442 L31.3429057,40.2337934 L31.3429057,44.6781293 L35.4869487,41.6351606 L35.4869487,66.3993207 L39.6710307,66.3993207 Z M58.3479838,66.6395551 C60.616864,66.6128624 62.5454122,65.8788129 64.1336283,64.4374067 C65.7485372,62.9826541 66.5826843,60.9139692 66.6360697,58.231352 L66.6360697,45.3587934 C66.5826843,42.6628298 65.7485372,40.5874718 64.1336283,39.1327192 C62.5454122,37.6913129 60.616864,36.9706098 58.3479838,36.9706098 C56.1458354,36.9706098 54.2506531,37.6913129 52.6624369,39.1327192 C50.96745,40.5874718 50.1066101,42.6628298 50.0799174,45.3587934 L50.0799174,58.231352 C50.1066101,60.9139692 50.96745,62.9826541 52.6624369,64.4374067 C54.2506531,65.8788129 56.1458354,66.6128624 58.3479838,66.6395551 Z M58.3479838,62.4554731 C55.678713,62.4287804 54.3173849,60.9406619 54.2639994,57.9911176 L54.2639994,45.6190473 C54.3173849,42.6961957 55.678713,41.2080772 58.3479838,41.1546918 C61.0039083,41.2080772 62.3719096,42.6961957 62.4519877,45.6190473 L62.4519877,57.9911176 C62.3719096,60.9406619 61.0039083,62.4287804 58.3479838,62.4554731 Z\" id=\"path-5\"></path>\n"
        , "<path d=\"M39.7001576,66.8158547 L39.7001576,37.6273781 L35.5160755,37.6273781 L31.3720326,40.6503273 L31.3720326,45.0946633 L35.5160755,42.0516945 L35.5160755,66.8158547 L39.7001576,66.8158547 Z M60.6793568,66.8158547 L60.6793568,37.6273781 L56.4952748,37.6273781 L52.3512318,40.6503273 L52.3512318,45.0946633 L56.4952748,42.0516945 L56.4952748,66.8158547 L60.6793568,66.8158547 Z\" id=\"path-5\"></path>\n"
        , "<path d=\"M39.0984005,66.4898724 L39.0984005,37.3013959 L34.9143185,37.3013959 L30.7702755,40.3243451 L30.7702755,44.768681 L34.9143185,41.7257123 L34.9143185,66.4898724 L39.0984005,66.4898724 Z M65.9032833,66.4898724 L65.9032833,62.3057904 L54.7524044,62.3057904 L64.1415646,50.534306 C65.3160437,49.0395144 65.9032833,47.331181 65.9032833,45.409306 C65.8765906,43.0069623 65.0691362,41.0250287 63.48092,39.4635053 C61.9193966,37.8886355 59.8840776,37.0878542 57.374963,37.0611615 C55.1327755,37.0878542 53.2442664,37.8752891 51.7094357,39.4234662 C50.1879513,41.0116823 49.3738237,43.0203086 49.2670529,45.4493451 L53.4311154,45.4493451 C53.5779253,44.1147097 54.0450477,43.0803672 54.8324825,42.3463178 C55.5932247,41.6122683 56.5474891,41.2452435 57.6952755,41.2452435 C58.9898719,41.2719362 59.9908484,41.6923464 60.6982052,42.506474 C61.3788693,43.3206016 61.7192013,44.2748659 61.7192013,45.369267 C61.7192013,45.783004 61.6658159,46.2234336 61.559045,46.690556 C61.3988888,47.1843711 61.0985958,47.7182253 60.6581661,48.2921185 L49.2670529,62.5460248 L49.2670529,66.4898724 L65.9032833,66.4898724 Z\" id=\"path-5\"></path>\n"
        , "<path d=\"M39.1164814,66.5082067 L39.1164814,37.3197302 L34.9323994,37.3197302 L30.7883564,40.3426794 L30.7883564,44.7870153 L34.9323994,41.7440466 L34.9323994,66.5082067 L39.1164814,66.5082067 Z M56.7724384,66.7484411 C59.3482848,66.7217484 61.4570087,65.9009476 63.0986103,64.2860388 C64.7669046,62.711169 65.6143981,60.5557328 65.6410908,57.8197302 C65.6410908,56.5918656 65.3808369,55.4173864 64.8603291,54.2962927 C64.3131285,53.1751989 63.425596,52.2142614 62.1977314,51.4134802 C63.3989033,50.5993526 64.2330504,49.6717809 64.7001728,48.6307653 C65.1005634,47.5897497 65.3007587,46.508695 65.3007587,45.3876013 C65.274066,43.1721065 64.5133238,41.2502315 63.0185322,39.6219763 C61.4570087,37.953682 59.3482848,37.1061885 56.6923603,37.0794958 C54.5969827,37.1061885 52.7952249,37.8535843 51.2870869,39.3216833 C49.7522561,40.8031286 48.8780699,42.6249059 48.6645283,44.7870153 L52.8486103,44.7870153 C53.1155374,43.5858434 53.6360452,42.6983109 54.4101337,42.1244177 C55.1441832,41.5505244 55.9583108,41.2635778 56.8525166,41.2635778 C58.0937275,41.2902705 59.1080504,41.683988 59.8954853,42.4447302 C60.6829202,43.2321651 61.089984,44.2398148 61.1166767,45.4676794 C61.1166767,46.6821976 60.7229593,47.6698278 59.9355244,48.43057 C59.1480895,49.2180049 58.0403421,49.6117224 56.6122822,49.6117224 L55.1708759,49.6117224 L55.1708759,53.3153356 L56.9325947,53.3153356 C58.227191,53.3153356 59.2948994,53.7090531 60.1357197,54.496488 C60.9898863,55.3106156 61.430316,56.458402 61.4570087,57.9398474 C61.430316,59.3946 60.9898863,60.5156937 60.1357197,61.3031286 C59.2948994,62.1439489 58.2605569,62.5643591 57.0326923,62.5643591 C55.8849059,62.5643591 54.9640074,62.2507198 54.269997,61.6234411 C53.589333,61.0228552 53.062152,60.2354203 52.6884541,59.2611364 L48.504372,59.2611364 C48.9581481,61.6901729 49.9524515,63.5453161 51.4872822,64.8265661 C53.0221129,66.1078161 54.7838317,66.7484411 56.7724384,66.7484411 Z\" id=\"path-5\"></path>\n"
        , "<path d=\"M39.7918762,67.2220839 L39.7918762,38.0336074 L35.6077942,38.0336074 L31.4637512,41.0565566 L31.4637512,45.5008925 L35.6077942,42.4579238 L35.6077942,67.2220839 L39.7918762,67.2220839 Z M65.4956848,67.2220839 L65.4956848,62.8778456 L67.8179504,62.8778456 L67.8179504,58.933998 L65.4956848,58.933998 L65.4956848,50.5458144 L61.3116028,50.5458144 L61.3116028,58.933998 L53.6841614,58.933998 L63.5537903,38.0336074 L58.8892395,38.0336074 L49.1397278,58.933998 L49.1397278,62.8778456 L61.3116028,62.8778456 L61.3116028,67.2220839 L65.4956848,67.2220839 Z\" id=\"path-5\"></path>\n"
        , "<path d=\"M39.6875529,66.3953656 L39.6875529,37.206889 L35.5034709,37.206889 L31.3594279,40.2298382 L31.3594279,44.6741742 L35.5034709,41.6312054 L35.5034709,66.3953656 L39.6875529,66.3953656 Z M58.5847209,66.6356 C60.0528199,66.6089072 61.3207235,66.2685752 62.3884318,65.6146039 C63.4828329,64.9873252 64.31698,64.2933148 64.8908733,63.5325726 C65.5448446,62.7584841 66.011967,61.8709515 66.2922404,60.869975 C66.5591675,59.855652 66.6926311,58.4008994 66.6926311,56.5057171 C66.6926311,55.2778526 66.652592,54.2435101 66.5725139,53.4026898 C66.4924358,52.5752158 66.3589722,51.8745322 66.1721233,51.300639 C65.8117717,50.246277 65.2245321,49.3187054 64.4104045,48.5179242 C63.863204,47.9173382 63.149174,47.4101768 62.2683147,46.9964398 C61.3340699,46.6227419 60.3064006,46.4225466 59.1853068,46.3958539 C57.4369345,46.3958539 55.8820842,46.929708 54.5207561,47.9974164 L54.5207561,41.390971 L66.1320842,41.390971 L66.1320842,37.206889 L50.5769084,37.206889 L50.5769084,52.9822796 L54.5207561,52.9822796 C55.0145712,52.0880739 55.575118,51.4607953 56.2023967,51.1004437 C56.8296753,50.7534385 57.5236858,50.5799359 58.2844279,50.5799359 C59.1385946,50.5799359 59.8526246,50.7000531 60.4265178,50.9402875 C61.000411,51.2339072 61.4341675,51.6276247 61.7277873,52.1214398 C62.2749878,53.1624554 62.5352417,54.5171104 62.508549,56.1854046 C62.508549,56.8660687 62.4952027,57.5667523 62.46851,58.2874554 C62.4151246,59.0215049 62.2683147,59.702169 62.0280803,60.3294476 C61.8011923,60.9567263 61.4008016,61.4638877 60.8269084,61.850932 C60.2263225,62.2513226 59.4255412,62.4515179 58.4245647,62.4515179 C56.0889527,62.4248252 54.6942587,61.2903851 54.2404826,59.0481976 L50.0564006,59.0481976 C50.4567912,61.7308148 51.4911337,63.6727093 53.1594279,64.8738812 C54.8010295,66.0483604 56.6094605,66.6356 58.5847209,66.6356 Z\" id=\"path-5\"></path>\n"
        , "<path d=\"M39.6666942,66.3716259 L39.6666942,37.1831494 L35.4826122,37.1831494 L31.3385692,40.2060986 L31.3385692,44.6504345 L35.4826122,41.6074658 L35.4826122,66.3716259 L39.6666942,66.3716259 Z M55.841206,66.3516064 C56.7754508,66.6185335 57.8498323,66.6986116 59.0643505,66.5918408 C60.2254833,66.4984163 61.2264599,66.2515087 62.0672802,65.8511181 C62.9081005,65.4507275 63.6221304,64.9735953 64.20937,64.4197216 C64.7966096,63.8658479 65.2704052,63.2786083 65.6307567,62.6580029 C65.9911083,62.0373974 66.2680452,61.456831 66.4615673,60.9163037 C66.6550894,60.3757763 66.788553,59.9086539 66.8619579,59.5149365 C66.9353629,59.121219 66.9787385,58.8776481 66.9920849,58.7842236 C67.0054312,58.5973746 67.015441,58.3704866 67.0221142,58.1035595 C67.0287873,57.8366324 67.0321239,57.5730419 67.0321239,57.312788 L67.0321239,57.312788 L67.0321239,56.161665 C67.0321239,55.9080843 66.9954215,55.5577425 66.9220165,55.1106396 C66.8486116,54.6635367 66.7184846,54.183068 66.5316357,53.6692333 C66.3447867,53.1553987 66.0878694,52.6282177 65.7608837,52.0876904 C65.433898,51.547163 65.0234976,51.0566845 64.5296825,50.6162548 C64.0358674,50.1758251 63.4452913,49.8088004 62.757954,49.5151806 C62.0706168,49.2215608 61.2664989,49.0614046 60.3456005,49.0347119 C59.7049755,49.0080192 59.141092,49.0180289 58.6539501,49.0647412 C58.1668082,49.1114534 57.7297151,49.2482535 57.3426708,49.4751415 C57.5295198,49.154829 57.763081,48.7277457 58.0433544,48.1938915 C58.3236278,47.6600374 58.6339306,47.0661246 58.9742626,46.4121533 C59.3145946,45.7581819 59.668273,45.0708447 60.0352978,44.3501415 C60.4023225,43.6294384 60.7626741,42.9154085 61.1163524,42.2080517 L61.1163524,42.2080517 L61.6343578,41.172041 C61.8028555,40.8350455 61.9671825,40.5063915 62.1273388,40.186079 C62.4476513,39.545454 62.7279247,38.978234 62.9681591,38.4844189 C63.2083935,37.9906038 63.398579,37.6002229 63.5387157,37.3132763 C63.6788524,37.0263297 63.7422476,36.8828564 63.7289013,36.8828564 L63.7289013,36.8828564 L59.0042919,36.8828564 C57.7096955,39.4853955 56.5418896,41.841027 55.5008739,43.9497509 C55.0604442,44.857303 54.6200146,45.7515087 54.1795849,46.6323681 C53.7391552,47.5132275 53.3421011,48.3206819 52.9884228,49.0547314 C52.6347444,49.7887809 52.337788,50.4060498 52.0975536,50.906538 C51.8573192,51.4070263 51.7105093,51.7240022 51.6571239,51.8574658 C51.4435823,52.3913199 51.2567333,52.885135 51.0965771,53.3389111 C50.9364208,53.7926871 50.7929475,54.2531363 50.6661571,54.7202587 C50.5393668,55.1873811 50.4292593,55.6778596 50.3358349,56.1916943 C50.2424104,56.7055289 50.1556591,57.2894319 50.075581,57.9434033 C50.0221955,58.383833 50.0388785,58.9176871 50.1256298,59.5449658 C50.2123811,60.1722444 50.3858837,60.8262158 50.6461376,61.5068798 C50.9063915,62.1875439 51.2634065,62.851525 51.7171825,63.4988232 C52.1709586,64.1461214 52.7381786,64.716678 53.4188427,65.2104931 C54.0995067,65.7043082 54.9069612,66.0846793 55.841206,66.3516064 Z M58.8966359,62.3078654 L58.6439403,62.3076611 C57.8965445,62.3076611 57.2726024,62.2142366 56.7721142,62.0273876 C56.2716259,61.8405387 55.8612255,61.6003043 55.540913,61.3066845 C55.2206005,61.0130647 54.9736929,60.6760693 54.8001903,60.2956982 C54.6266877,59.9153271 54.503234,59.5216096 54.429829,59.1145458 C54.3564241,58.707482 54.3130484,58.3004182 54.2997021,57.8933544 C54.2863557,57.4862906 54.2796825,57.1159293 54.2796825,56.7822705 C54.2796825,56.6354606 54.3130484,56.4252555 54.3797802,56.1516552 C54.446512,55.878055 54.5566194,55.5810986 54.7101024,55.2607861 C54.8635855,54.9404736 55.0571077,54.6168245 55.2906689,54.2898388 C55.5242301,53.9628531 55.8011669,53.6725699 56.1214794,53.4189892 C56.4417919,53.1654085 56.8088166,52.9652132 57.2225536,52.8184033 C57.6362906,52.6715934 58.0967398,52.618208 58.6039013,52.658247 C59.684956,52.7383251 60.5191031,52.9785595 61.1063427,53.3789501 C61.6935823,53.7793408 62.117329,54.2564729 62.3775829,54.8103466 C62.6378368,55.3642203 62.7879833,55.9481233 62.8280224,56.5620556 C62.8680614,57.1759879 62.888081,57.7365348 62.888081,58.2436962 C62.888081,58.3638134 62.8680614,58.5473258 62.8280224,58.7942333 C62.7879833,59.0411409 62.7112418,59.3180777 62.5977978,59.6250439 C62.4843538,59.93201 62.3275341,60.2456494 62.1273388,60.5659619 C61.9271435,60.8862744 61.6702261,61.1765576 61.3565868,61.4368115 C61.0429475,61.6970654 60.6625764,61.910607 60.2154735,62.0774365 C59.7683707,62.2442659 59.2445263,62.3210074 58.6439403,62.3076611 L58.8966359,62.3078654 Z\" id=\"path-5\"></path>\n"
        , "<path d=\"M39.1115133,66.4371591 L39.1115133,37.2486826 L34.9274312,37.2486826 L30.7833883,40.2716318 L30.7833883,44.7159677 L34.9274312,41.672999 L34.9274312,66.4371591 L39.1115133,66.4371591 Z M56.7674703,66.4371591 L66.6571187,41.4327646 L66.6571187,37.2486826 L50.0409078,37.2486826 L50.0409078,45.6568857 L54.2249898,45.6568857 L54.2249898,41.4327646 L61.9725484,41.4327646 L52.1029195,66.4371591 L56.7674703,66.4371591 Z\" id=\"path-5\"></path>\n"
        , "<path d=\"M39.6680108,66.3561349 L39.6680108,37.1676584 L35.4839288,37.1676584 L31.3398858,40.1906076 L31.3398858,44.6349435 L35.4839288,41.5919748 L35.4839288,66.3561349 L39.6680108,66.3561349 Z M58.344964,66.5963693 C60.8407322,66.5696766 62.9494562,65.7488758 64.6711358,64.133967 C66.3661228,62.5590972 67.2269627,60.4570464 67.2536554,57.8278146 C67.2269627,55.0117339 66.0791762,52.8229318 63.810296,51.2614084 C64.677809,50.5006662 65.3851658,49.62648 65.9323663,48.6388498 C66.4528741,47.7179513 66.713128,46.6235503 66.713128,45.3556467 C66.6864353,42.9266102 65.8923272,40.9179839 64.3308038,39.3297677 C62.7826267,37.754898 60.7873468,36.9541167 58.344964,36.927424 C55.9426202,36.9541167 53.9807062,37.754898 52.4592218,39.3297677 C50.8443129,40.9179839 50.0235121,42.9266102 49.9968194,45.3556467 C49.9968194,46.6235503 50.2837661,47.7179513 50.8576593,48.6388498 C51.3514744,49.62648 52.0321384,50.5006662 52.8996515,51.2614084 C50.6307713,52.8229318 49.4829848,55.0117339 49.4562921,57.8278146 C49.4829848,60.4570464 50.3705174,62.5590972 52.1188897,64.133967 C53.787184,65.7488758 55.8625421,66.5696766 58.344964,66.5963693 Z M58.344964,49.4596506 C57.2639093,49.4596506 56.309645,49.0926258 55.482171,48.3585763 C54.6413507,47.6512196 54.2075942,46.6235503 54.1809015,45.2755685 C54.2075942,43.8875477 54.6413507,42.8398589 55.482171,42.1325021 C56.309645,41.4518381 57.2639093,41.111506 58.344964,41.111506 C59.4660577,41.111506 60.4403416,41.4518381 61.2678155,42.1325021 C62.0819431,42.8398589 62.5023533,43.8875477 62.529046,45.2755685 C62.5023533,46.6235503 62.0819431,47.6512196 61.2678155,48.3585763 C60.4403416,49.0926258 59.4660577,49.4596506 58.344964,49.4596506 Z M58.344964,62.4122873 C57.063714,62.4122873 55.9759861,61.9651844 55.0817804,61.0709787 C54.1475356,60.216812 53.6670668,59.122411 53.6403741,57.7877756 C53.6670668,56.4264474 54.1475356,55.3187 55.0817804,54.4645334 C55.9759861,53.6237131 57.063714,53.1899566 58.344964,53.1632638 C59.666253,53.1899566 60.7740004,53.6237131 61.6682062,54.4645334 C62.5757582,55.3187 63.0428806,56.4264474 63.0695733,57.7877756 C63.0428806,59.122411 62.5757582,60.216812 61.6682062,61.0709787 C60.7740004,61.9651844 59.666253,62.4122873 58.344964,62.4122873 Z\" id=\"path-5\"></path>\n"
        , "<path d=\"M39.6602857,66.4132312 L39.6602857,37.2247546 L35.4762037,37.2247546 L31.3321607,40.2477038 L31.3321607,44.6920398 L35.4762037,41.649071 L35.4762037,66.4132312 L39.6602857,66.4132312 Z M57.6165357,66.4132312 C58.8844393,63.8507312 60.0322258,61.5284655 61.0598951,59.4464343 C61.5003248,58.5655749 61.9307447,57.6880521 62.3511548,56.8138659 C62.771565,55.9396797 63.1586093,55.1455717 63.5122877,54.4315417 C63.865966,53.7175118 64.1595858,53.1135892 64.393147,52.6197741 C64.6267082,52.125959 64.7701815,51.8056465 64.823567,51.6588366 C65.0371086,51.1249825 65.2239576,50.6311674 65.3841138,50.1773913 C65.5442701,49.7236153 65.6877434,49.2631661 65.8145337,48.7960437 C65.9413241,48.3289213 66.0514315,47.8384428 66.144856,47.3246081 C66.2382805,46.8107735 66.3250318,46.2268705 66.4051099,45.5728991 C66.4584953,45.1324695 66.445149,44.6019519 66.3650709,43.9813464 C66.2849927,43.3607409 66.1214999,42.7201159 65.8745923,42.0594714 C65.6276848,41.3988269 65.2840162,40.7481921 64.8435865,40.1075671 C64.4031568,39.4669421 63.8492831,38.9063952 63.1819654,38.4259265 C62.5146477,37.9454577 61.717203,37.578433 60.7896314,37.3248523 C59.8620598,37.0712715 58.7910149,36.9978666 57.5764966,37.1046374 C56.4153638,37.2114083 55.4110507,37.4583158 54.5635572,37.8453601 C53.7160637,38.2324043 53.0020337,38.6928536 52.4214673,39.2267077 C51.8409009,39.7605619 51.370442,40.3311185 51.0100904,40.9383777 C50.6497388,41.5456368 50.3694654,42.1161934 50.1692701,42.6500476 C49.9690748,43.1839017 49.8322746,43.6410144 49.7588697,44.0213855 C49.6854647,44.4017566 49.6420891,44.6386543 49.6287427,44.7320788 C49.60205,44.9189278 49.5853671,45.1458158 49.5786939,45.4127429 C49.5720207,45.67967 49.5686841,45.9432605 49.5686841,46.2035144 C49.5686841,46.4637683 49.5720207,46.7006661 49.5786939,46.9142077 C49.5853671,47.1277494 49.5887037,47.2745593 49.5887037,47.3546374 C49.5887037,47.6082182 49.6320793,47.9585599 49.7188306,48.4056628 C49.8055819,48.8527657 49.9457186,49.3332344 50.1392408,49.8470691 C50.3327629,50.3609037 50.5896802,50.8880847 50.9099927,51.428612 C51.2303052,51.9691394 51.6306959,52.4596179 52.1111646,52.9000476 C52.5916334,53.3404773 53.16219,53.707502 53.8228345,54.0011218 C54.4834791,54.2947416 55.254231,54.4548978 56.1350904,54.4815905 C56.7757154,54.5082833 57.3395989,54.4982735 57.8267408,54.4515613 C58.3138827,54.404849 58.7509758,54.2680489 59.1380201,54.0411609 C58.8844393,54.4815905 58.5541171,55.0888497 58.1470533,55.8629382 C57.7399895,56.6370267 57.3028964,57.4845202 56.835774,58.4054187 C56.3686516,59.3263171 55.894856,60.2605619 55.4143873,61.208153 C54.9339185,62.1557442 54.500162,63.016584 54.1131177,63.7906726 C53.7260735,64.5647611 53.4124341,65.1953764 53.1721998,65.6825183 C52.9319654,66.1696602 52.8185214,66.4132312 52.8318677,66.4132312 L52.8318677,66.4132312 L57.6165357,66.4132312 Z M58.1264777,50.8639871 L57.8767896,50.8580554 C56.8624667,50.80467 56.0750318,50.5811185 55.5144849,50.1874011 C54.953938,49.7936836 54.5435377,49.3198881 54.2832837,48.7660144 C54.0230298,48.2121407 53.8662102,47.6215645 53.8128248,46.9942859 C53.7594393,46.3670072 53.7327466,45.793114 53.7327466,45.2726062 C53.7327466,45.1124499 53.7928052,44.8188301 53.9129224,44.3917468 C54.0330396,43.9646635 54.2465813,43.5242338 54.5535474,43.0704577 C54.8605136,42.6166817 55.2775871,42.2029447 55.8047681,41.8292468 C56.3319491,41.4555489 57.0092766,41.2486804 57.8367505,41.2086413 C58.5841464,41.1686023 59.2080884,41.2319974 59.7085767,41.3988269 C60.209065,41.5656563 60.6194654,41.7958809 60.9397779,42.0895007 C61.2600904,42.3831205 61.5069979,42.7267891 61.6805005,43.1205066 C61.8540031,43.514224 61.9774569,43.9246244 62.0508619,44.3517077 C62.1242668,44.7787911 62.1643059,45.1992012 62.1709791,45.6129382 C62.1776522,46.0266752 62.187662,46.4003731 62.2010084,46.734032 C62.2010084,46.8941882 62.1676425,47.1077299 62.1009107,47.374657 C62.0341789,47.641584 61.9240715,47.9318672 61.7705884,48.2455066 C61.6171054,48.5591459 61.4235832,48.8761218 61.190022,49.1964343 C60.9564608,49.5167468 60.679524,49.8036934 60.3592115,50.0572741 C60.038899,50.3108549 59.6718742,50.5143868 59.2581373,50.6678698 C58.8444003,50.8213529 58.3839511,50.8847481 57.8767896,50.8580554 L58.1264777,50.8639871 Z\" id=\"path-5\"></path>\n"
        , "<path d=\"M45.5087937,66.5720639 L45.5087937,62.3879819 L34.3579148,62.3879819 L43.7470749,50.6164975 C44.9215541,49.1217058 45.5087937,47.4133725 45.5087937,45.4914975 C45.482101,43.0891538 44.6746465,41.1072202 43.0864304,39.5456967 C41.5249069,37.9708269 39.4895879,37.1700457 36.9804733,37.143353 C34.7382858,37.1700457 32.8497767,37.9574806 31.314946,39.5056577 C29.7934616,41.0938738 28.979334,43.1025001 28.8725632,45.5315366 L33.0366257,45.5315366 C33.1834356,44.1969011 33.650558,43.1625587 34.4379929,42.4285092 C35.1987351,41.6944597 36.1529994,41.327435 37.3007858,41.327435 C38.5953822,41.3541277 39.5963588,41.7745379 40.3037155,42.5886655 C40.9843796,43.4027931 41.3247116,44.3570574 41.3247116,45.4514584 C41.3247116,45.8651954 41.2713262,46.3056251 41.1645554,46.7727475 C41.0043991,47.2665626 40.7041062,47.8004168 40.2636765,48.37431 L28.8725632,62.6282163 L28.8725632,66.5720639 L45.5087937,66.5720639 Z M58.3600632,66.8122983 C60.6289434,66.7856056 62.5574916,66.0515561 64.1457077,64.6101498 C65.7606166,63.1553972 66.5947637,61.0867123 66.6481491,58.4040952 L66.6481491,45.5315366 C66.5947637,42.835573 65.7606166,40.7602149 64.1457077,39.3054623 C62.5574916,37.8640561 60.6289434,37.143353 58.3600632,37.143353 C56.1579148,37.143353 54.2627325,37.8640561 52.6745163,39.3054623 C50.9795293,40.7602149 50.1186895,42.835573 50.0919968,45.5315366 L50.0919968,58.4040952 C50.1186895,61.0867123 50.9795293,63.1553972 52.6745163,64.6101498 C54.2627325,66.0515561 56.1579148,66.7856056 58.3600632,66.8122983 Z M58.3600632,62.6282163 C55.6907924,62.6015235 54.3294642,61.1134051 54.2760788,58.1638608 L54.2760788,45.7917905 C54.3294642,42.8689389 55.6907924,41.3808204 58.3600632,41.327435 C61.0159877,41.3808204 62.383989,42.8689389 62.4640671,45.7917905 L62.4640671,58.1638608 C62.383989,61.1134051 61.0159877,62.6015235 58.3600632,62.6282163 Z\" id=\"path-5\"></path>\n"
        , "<path d=\"M45.5212035,66.9897668 L45.5212035,62.8056848 L34.3703246,62.8056848 L43.7594847,51.0342004 C44.9339639,49.5394088 45.5212035,47.8310754 45.5212035,45.9092004 C45.4945108,43.5068567 44.6870563,41.5249231 43.0988402,39.9633996 C41.5373168,38.3885298 39.5019977,37.5877486 36.9928832,37.5610559 C34.7506957,37.5877486 32.8621865,38.3751835 31.3273558,39.9233606 C29.8058714,41.5115767 28.9917438,43.520203 28.884973,45.9492395 L33.0490355,45.9492395 C33.1958454,44.6146041 33.6629678,43.5802616 34.4504027,42.8462121 C35.2111449,42.1121627 36.1654092,41.7451379 37.3131957,41.7451379 C38.607792,41.7718306 39.6087686,42.1922408 40.3161253,43.0063684 C40.9967894,43.820496 41.3371214,44.7747603 41.3371214,45.8691614 C41.3371214,46.2828983 41.283736,46.723328 41.1769652,47.1904504 C41.0168089,47.6842655 40.716516,48.2181197 40.2760863,48.7920129 L28.884973,63.0459192 L28.884973,66.9897668 L45.5212035,66.9897668 Z M60.6747191,66.9897668 L60.6747191,37.8012903 L56.4906371,37.8012903 L52.3465941,40.8242395 L52.3465941,45.2685754 L56.4906371,42.2256067 L56.4906371,66.9897668 L60.6747191,66.9897668 Z\" id=\"path-5\"></path>\n"
        , "<path d=\"M45.4928559,66.5843776 L45.4928559,62.4002956 L34.341977,62.4002956 L43.7311371,50.6288112 C44.9056163,49.1340195 45.4928559,47.4256862 45.4928559,45.5038112 C45.4661632,43.1014674 44.6587088,41.1195338 43.0704926,39.5580104 C41.5089692,37.9831406 39.4736502,37.1823594 36.9645356,37.1556667 C34.7223481,37.1823594 32.833839,37.9697943 31.2990082,39.5179713 C29.7775239,41.1061875 28.9633963,43.1148138 28.8566254,45.5438503 L33.0206879,45.5438503 C33.1674978,44.2092148 33.6346202,43.1748724 34.4220551,42.4408229 C35.1827973,41.7067734 36.1370616,41.3397487 37.2848481,41.3397487 C38.5794444,41.3664414 39.580421,41.7868516 40.2877778,42.6009792 C40.9684418,43.4151068 41.3087739,44.3693711 41.3087739,45.4637721 C41.3087739,45.8775091 41.2553884,46.3179388 41.1486176,46.7850612 C40.9884614,47.2788763 40.6881684,47.8127305 40.2477387,48.3866237 L28.8566254,62.6405299 L28.8566254,66.5843776 L45.4928559,66.5843776 Z M66.4720551,66.5843776 L66.4720551,62.4002956 L55.3211762,62.4002956 L64.7103364,50.6288112 C65.8848155,49.1340195 66.4720551,47.4256862 66.4720551,45.5038112 C66.4453624,43.1014674 65.637908,41.1195338 64.0496918,39.5580104 C62.4881684,37.9831406 60.4528494,37.1823594 57.9437348,37.1556667 C55.7015473,37.1823594 53.8130382,37.9697943 52.2782075,39.5179713 C50.7567231,41.1061875 49.9425955,43.1148138 49.8358246,45.5438503 L53.9998871,45.5438503 C54.146697,44.2092148 54.6138194,43.1748724 55.4012543,42.4408229 C56.1619965,41.7067734 57.1162608,41.3397487 58.2640473,41.3397487 C59.5586437,41.3664414 60.5596202,41.7868516 61.266977,42.6009792 C61.9476411,43.4151068 62.2879731,44.3693711 62.2879731,45.4637721 C62.2879731,45.8775091 62.2345877,46.3179388 62.1278168,46.7850612 C61.9676606,47.2788763 61.6673676,47.8127305 61.2269379,48.3866237 L49.8358246,62.6405299 L49.8358246,66.5843776 L66.4720551,66.5843776 Z\" id=\"path-5\"></path>\n"
        , "<path d=\"M45.5109368,66.6027119 L45.5109368,62.4186299 L34.3600579,62.4186299 L43.749218,50.6471455 C44.9236972,49.1523538 45.5109368,47.4440205 45.5109368,45.5221455 C45.4842441,43.1198017 44.6767896,41.1378681 43.0885735,39.5763447 C41.5270501,38.0014749 39.491731,37.2006937 36.9826165,37.174001 C34.740429,37.2006937 32.8519198,37.9881286 31.3170891,39.5363056 C29.7956047,41.1245218 28.9814771,43.1331481 28.8747063,45.5621845 L33.0387688,45.5621845 C33.1855787,44.2275491 33.6527011,43.1932067 34.440136,42.4591572 C35.2008782,41.7251077 36.1551425,41.358083 37.302929,41.358083 C38.5975253,41.3847757 39.5985019,41.8051858 40.3058587,42.6193135 C40.9865227,43.4334411 41.3268547,44.3877054 41.3268547,45.4821064 C41.3268547,45.8958434 41.2734693,46.3362731 41.1666985,46.8033955 C41.0065422,47.2972106 40.7062493,47.8310648 40.2658196,48.404958 L28.8747063,62.6588642 L28.8747063,66.6027119 L45.5109368,66.6027119 Z M57.3412102,66.8429463 C59.9170566,66.8162536 62.0257805,65.9954528 63.6673821,64.3805439 C65.3356764,62.8056741 66.1831698,60.6502379 66.2098626,57.9142353 C66.2098626,56.6863707 65.9496087,55.5118916 65.4291008,54.3907978 C64.8819003,53.2697041 63.9943678,52.3087666 62.7665032,51.5079853 C63.9676751,50.6938577 64.8018222,49.7662861 65.2689446,48.7252705 C65.6693352,47.6842549 65.8695305,46.6032002 65.8695305,45.4821064 C65.8428378,43.2666116 65.0820956,41.3447366 63.587304,39.7164814 C62.0257805,38.0481871 59.9170566,37.2006937 57.2611321,37.174001 C55.1657545,37.2006937 53.3639967,37.9480895 51.8558587,39.4161885 C50.3210279,40.8976338 49.4468417,42.7194111 49.2333001,44.8815205 L53.4173821,44.8815205 C53.6843092,43.6803486 54.204817,42.7928161 54.9789055,42.2189228 C55.712955,41.6450296 56.5270826,41.358083 57.4212883,41.358083 C58.6624993,41.3847757 59.6768222,41.7784931 60.4642571,42.5392353 C61.251692,43.3266702 61.6587558,44.33432 61.6854485,45.5621845 C61.6854485,46.7767028 61.291731,47.764333 60.5042962,48.5250752 C59.7168613,49.3125101 58.6091139,49.7062275 57.181054,49.7062275 L55.7396477,49.7062275 L55.7396477,53.4098408 L57.5013665,53.4098408 C58.7959628,53.4098408 59.8636712,53.8035582 60.7044915,54.5909931 C61.5586581,55.4051207 61.9990878,56.5529072 62.0257805,58.0343525 C61.9990878,59.4891051 61.5586581,60.6101989 60.7044915,61.3976338 C59.8636712,62.2384541 58.8293287,62.6588642 57.6014641,62.6588642 C56.4536777,62.6588642 55.5327792,62.3452249 54.8387688,61.7179463 C54.1581047,61.1173603 53.6309238,60.3299254 53.2572258,59.3556416 L49.0731438,59.3556416 C49.5269198,61.784678 50.5212232,63.6398213 52.056054,64.9210713 C53.5908847,66.2023213 55.3526034,66.8429463 57.3412102,66.8429463 Z\" id=\"path-5\"></path>\n"
        , "<path d=\"M44.9302212,66.5776371 L44.9302212,62.3935551 L33.7793423,62.3935551 L43.1685025,50.6220707 C44.3429816,49.127279 44.9302212,47.4189457 44.9302212,45.4970707 C44.9035285,43.094727 44.0960741,41.1127934 42.5078579,39.5512699 C40.9463345,37.9764001 38.9110155,37.1756189 36.4019009,37.1489262 C34.1597134,37.1756189 32.2712043,37.9630538 30.7363736,39.5112309 C29.2148892,41.099447 28.4007616,43.1080733 28.2939907,45.5371098 L32.4580532,45.5371098 C32.6048631,44.2024744 33.0719855,43.1681319 33.8594204,42.4340824 C34.6201626,41.700033 35.5744269,41.3330082 36.7222134,41.3330082 C38.0168098,41.3597009 39.0177863,41.7801111 39.7251431,42.5942387 C40.4058072,43.4083663 40.7461392,44.3626306 40.7461392,45.4570316 C40.7461392,45.8707686 40.6927538,46.3111983 40.5859829,46.7783207 C40.4258267,47.2721358 40.1255337,47.80599 39.685104,48.3798832 L28.2939907,62.6337895 L28.2939907,66.5776371 L44.9302212,66.5776371 Z M64.8083462,66.5776371 L64.8083462,62.2333988 L67.1306118,62.2333988 L67.1306118,58.2895512 L64.8083462,58.2895512 L64.8083462,49.9013676 L60.6242642,49.9013676 L60.6242642,58.2895512 L52.9968228,58.2895512 L62.8664517,37.3891606 L58.2019009,37.3891606 L48.4523892,58.2895512 L48.4523892,62.2333988 L60.6242642,62.2333988 L60.6242642,66.5776371 L64.8083462,66.5776371 Z\" id=\"path-5\"></path>\n"
        , "<path d=\"M44.9519064,66.6475157 L44.9519064,62.4634337 L33.8010275,62.4634337 L43.1901877,50.6919493 C44.3646668,49.1971577 44.9519064,47.4888243 44.9519064,45.5669493 C44.9252137,43.1646056 44.1177593,41.182672 42.5295431,39.6211486 C40.9680197,38.0462788 38.9327007,37.2454975 36.4235861,37.2188048 C34.1813986,37.2454975 32.2928895,38.0329324 30.7580588,39.5811095 C29.2365744,41.1693256 28.4224468,43.1779519 28.315676,45.6069884 L32.4797385,45.6069884 C32.6265484,44.272353 33.0936708,43.2380105 33.8811056,42.5039611 C34.6418478,41.7699116 35.5961122,41.4028868 36.7438986,41.4028868 C38.038495,41.4295795 39.0394715,41.8499897 39.7468283,42.6641173 C40.4274924,43.4782449 40.7678244,44.4325092 40.7678244,45.5269103 C40.7678244,45.9406473 40.714439,46.3810769 40.6076681,46.8481993 C40.4475119,47.3420144 40.1472189,47.8758686 39.7067892,48.4497618 L28.315676,62.7036681 L28.315676,66.6475157 L44.9519064,66.6475157 Z M58.0233908,66.8877501 C59.4914898,66.8610574 60.7593934,66.5207254 61.8271017,65.866754 C62.9215028,65.2394754 63.7556499,64.545465 64.3295431,63.7847228 C64.9835145,63.0106342 65.4506369,62.1231017 65.7309103,61.1221251 C65.9978374,60.1078022 66.131301,58.6530496 66.131301,56.7578673 C66.131301,55.5300027 66.0912619,54.4956603 66.0111838,53.65484 C65.9311056,52.827366 65.7976421,52.1266824 65.6107931,51.5527892 C65.2504416,50.4984272 64.663202,49.5708556 63.8490744,48.7700743 C63.3018739,48.1694884 62.5878439,47.6623269 61.7069846,47.24859 C60.7727398,46.874892 59.7450705,46.6746967 58.6239767,46.648004 C56.8756043,46.648004 55.3207541,47.1818582 53.959426,48.2495665 L53.959426,41.6431212 L65.5707541,41.6431212 L65.5707541,37.4590392 L50.0155783,37.4590392 L50.0155783,53.2344298 L53.959426,53.2344298 C54.4532411,52.3402241 55.0137879,51.7129454 55.6410666,51.3525939 C56.2683452,51.0055887 56.9623556,50.8320861 57.7230978,50.8320861 C58.5772645,50.8320861 59.2912944,50.9522032 59.8651877,51.1924376 C60.4390809,51.4860574 60.8728374,51.8797749 61.1664572,52.37359 C61.7136577,53.4146056 61.9739116,54.7692605 61.9472189,56.4375548 C61.9472189,57.1182189 61.9338726,57.8189025 61.9071799,58.5396056 C61.8537944,59.2736551 61.7069846,59.9543191 61.4667502,60.5815978 C61.2398622,61.2088764 60.8394715,61.7160379 60.2655783,62.1030821 C59.6649924,62.5034728 58.8642111,62.7036681 57.8632346,62.7036681 C55.5276226,62.6769754 54.1329286,61.5425353 53.6791525,59.3003478 L49.4950705,59.3003478 C49.8954611,61.982965 50.9298036,63.9248595 52.5980978,65.1260314 C54.2396994,66.3005105 56.0481304,66.8877501 58.0233908,66.8877501 Z\" id=\"path-5\"></path>\n"
        , "<path d=\"M45.5044572,66.5443691 L45.5044572,62.3602871 L34.3535783,62.3602871 L43.7427385,50.5888027 C44.9172176,49.094011 45.5044572,47.3856777 45.5044572,45.4638027 C45.4777645,43.0614589 44.6703101,41.0795254 43.0820939,39.5180019 C41.5205705,37.9431321 39.4852515,37.1423509 36.9761369,37.1156582 C34.7339494,37.1423509 32.8454403,37.9297858 31.3106096,39.4779629 C29.7891252,41.066179 28.9749976,43.0748053 28.8682268,45.5038418 L33.0322893,45.5038418 C33.1790991,44.1692063 33.6462215,43.1348639 34.4336564,42.4008144 C35.1943986,41.6667649 36.1486629,41.2997402 37.2964494,41.2997402 C38.5910458,41.3264329 39.5920223,41.7468431 40.2993791,42.5609707 C40.9800432,43.3750983 41.3203752,44.3293626 41.3203752,45.4237636 C41.3203752,45.8375006 41.2669898,46.2779303 41.1602189,46.7450527 C41.0000627,47.2388678 40.6997697,47.772722 40.25934,48.3466152 L28.8682268,62.6005214 L28.8682268,66.5443691 L45.5044572,66.5443691 Z M55.8532853,66.5243496 C56.7875301,66.7912767 57.8619116,66.8713548 59.0764299,66.7645839 C60.2375627,66.6711595 61.2385393,66.4242519 62.0793596,66.0238613 C62.9201799,65.6234707 63.6342098,65.1463385 64.2214494,64.5924648 C64.808689,64.0385911 65.2824846,63.4513515 65.6428361,62.8307461 C66.0031877,62.2101406 66.2801245,61.6295742 66.4736467,61.0890468 C66.6671688,60.5485195 66.8006323,60.0813971 66.8740373,59.6876796 C66.9474422,59.2939622 66.9908179,59.0503912 67.0041643,58.9569668 C67.0175106,58.7701178 67.0275204,58.5432298 67.0341935,58.2763027 C67.0408667,58.0093756 67.0442033,57.7457851 67.0442033,57.4855312 L67.0442033,57.4855312 L67.0442033,56.3344082 C67.0442033,56.0808274 67.0075008,55.7304856 66.9340959,55.2833828 C66.8606909,54.8362799 66.730564,54.3558112 66.543715,53.8419765 C66.3568661,53.3281419 66.0999488,52.8009609 65.7729631,52.2604336 C65.4459774,51.7199062 65.035577,51.2294277 64.5417619,50.788998 C64.0479468,50.3485683 63.4573706,49.9815436 62.7700334,49.6879238 C62.0826962,49.394304 61.2785783,49.2341477 60.3576799,49.207455 C59.7170549,49.1807623 59.1531714,49.1907721 58.6660295,49.2374843 C58.1788876,49.2841966 57.7417945,49.4209967 57.3547502,49.6478847 C57.5415991,49.3275722 57.7751603,48.9004889 58.0554338,48.3666347 C58.3357072,47.8327806 58.64601,47.2388678 58.986342,46.5848964 C59.326674,45.9309251 59.6803524,45.2435879 60.0473771,44.5228847 C60.4144019,43.8021816 60.7747534,43.0881517 61.1284318,42.3807949 L61.1284318,42.3807949 L61.6464372,41.3447841 C61.8149349,41.0077887 61.9792619,40.6791347 62.1394182,40.3588222 C62.4597307,39.7181972 62.7400041,39.1509772 62.9802385,38.6571621 C63.2204728,38.163347 63.4106584,37.7729661 63.5507951,37.4860195 C63.6909318,37.1990729 63.754327,37.0555996 63.7409807,37.0555996 L63.7409807,37.0555996 L59.0163713,37.0555996 C57.7217749,39.6581386 56.5539689,42.0137701 55.5129533,44.1224941 C55.0725236,45.0300462 54.6320939,45.9242519 54.1916643,46.8051113 C53.7512346,47.6859707 53.3541805,48.4934251 53.0005021,49.2274746 C52.6468238,49.961524 52.3498674,50.5787929 52.109633,51.0792812 C51.8693986,51.5797695 51.7225887,51.8967454 51.6692033,52.0302089 C51.4556616,52.5640631 51.2688127,53.0578782 51.1086564,53.5116543 C50.9485002,53.9654303 50.8050269,54.4258795 50.6782365,54.8930019 C50.5514462,55.3601243 50.4413387,55.8506028 50.3479143,56.3644375 C50.2544898,56.8782721 50.1677385,57.4621751 50.0876603,58.1161464 C50.0342749,58.5565761 50.0509579,59.0904303 50.1377092,59.7177089 C50.2244605,60.3449876 50.3979631,60.9989589 50.658217,61.679623 C50.9184709,62.3602871 51.2754859,63.0242682 51.7292619,63.6715664 C52.1830379,64.3188645 52.750258,64.8894212 53.4309221,65.3832363 C54.1115861,65.8770514 54.9190406,66.2574225 55.8532853,66.5243496 Z M58.9087152,62.4806085 L58.6560197,62.4804043 C57.9086239,62.4804043 57.2846818,62.3869798 56.7841935,62.2001308 C56.2837053,62.0132819 55.8733049,61.7730475 55.5529924,61.4794277 C55.2326799,61.1858079 54.9857723,60.8488125 54.8122697,60.4684414 C54.6387671,60.0880703 54.5153133,59.6943528 54.4419084,59.287289 C54.3685034,58.8802252 54.3251278,58.4731614 54.3117814,58.0660976 C54.2984351,57.6590338 54.2917619,57.2886725 54.2917619,56.9550136 C54.2917619,56.8082037 54.3251278,56.5979987 54.3918596,56.3243984 C54.4585913,56.0507981 54.5686988,55.7538418 54.7221818,55.4335293 C54.8756649,55.1132168 55.069187,54.7895677 55.3027482,54.462582 C55.5363094,54.1355963 55.8132463,53.8453131 56.1335588,53.5917324 C56.4538713,53.3381517 56.820896,53.1379563 57.234633,52.9911464 C57.64837,52.8443365 58.1088192,52.7909511 58.6159807,52.8309902 C59.6970353,52.9110683 60.5311825,53.1513027 61.1184221,53.5516933 C61.7056616,53.9520839 62.1294084,54.4292161 62.3896623,54.9830898 C62.6499162,55.5369635 62.8000627,56.1208665 62.8401018,56.7347988 C62.8801408,57.3487311 62.9001603,57.909278 62.9001603,58.4164394 C62.9001603,58.5365566 62.8801408,58.720069 62.8401018,58.9669765 C62.8000627,59.2138841 62.7233212,59.4908209 62.6098771,59.7977871 C62.4964331,60.1047532 62.3396135,60.4183925 62.1394182,60.738705 C61.9392228,61.0590175 61.6823055,61.3493007 61.3686662,61.6095546 C61.0550269,61.8698086 60.6746558,62.0833502 60.2275529,62.2501796 C59.7804501,62.4170091 59.2566057,62.4937506 58.6560197,62.4804043 L58.9087152,62.4806085 Z\" id=\"path-5\"></path>\n"
        , "<path d=\"M44.9325592,66.6110713 L44.9325592,62.4269892 L33.7816803,62.4269892 L43.1708404,50.6555049 C44.3453196,49.1607132 44.9325592,47.4523799 44.9325592,45.5305049 C44.9058665,43.1281611 44.098412,41.1462275 42.5101959,39.5847041 C40.9486724,38.0098343 38.9133534,37.209053 36.4042388,37.1823603 C34.1620513,37.209053 32.2735422,37.9964879 30.7387115,39.544665 C29.2172271,41.1328812 28.4030995,43.1415075 28.2963287,45.5705439 L32.4603912,45.5705439 C32.6072011,44.2359085 33.0743235,43.2015661 33.8617584,42.4675166 C34.6225006,41.7334671 35.5767649,41.3664424 36.7245513,41.3664424 C38.0191477,41.3931351 39.0201243,41.8135452 39.727481,42.6276728 C40.4081451,43.4418004 40.7484771,44.3960648 40.7484771,45.4904658 C40.7484771,45.9042028 40.6950917,46.3446325 40.5883209,46.8117549 C40.4281646,47.30557 40.1278717,47.8394241 39.687442,48.4133174 L28.2963287,62.6672236 L28.2963287,66.6110713 L44.9325592,66.6110713 Z M56.7628326,66.6110713 L66.652481,41.6066767 L66.652481,37.4225947 L50.0362701,37.4225947 L50.0362701,45.8307978 L54.2203521,45.8307978 L54.2203521,41.6066767 L61.9679107,41.6066767 L52.0982818,66.6110713 L56.7628326,66.6110713 Z\" id=\"path-5\"></path>\n"
        , "<path d=\"M45.5057738,66.5288781 L45.5057738,62.3447961 L34.3548949,62.3447961 L43.7440551,50.5733117 C44.9185342,49.07852 45.5057738,47.3701867 45.5057738,45.4483117 C45.4790811,43.045968 44.6716267,41.0640344 43.0834105,39.5025109 C41.5218871,37.9276411 39.4865681,37.1268599 36.9774535,37.1001672 C34.735266,37.1268599 32.8467569,37.9142948 31.3119262,39.4624719 C29.7904418,41.050688 28.9763142,43.0593143 28.8695433,45.4883508 L33.0336058,45.4883508 C33.1804157,44.1537154 33.6475381,43.1193729 34.434973,42.3853234 C35.1957152,41.6512739 36.1499795,41.2842492 37.297766,41.2842492 C38.5923624,41.3109419 39.5933389,41.7313521 40.3006957,42.5454797 C40.9813598,43.3596073 41.3216918,44.3138716 41.3216918,45.4082726 C41.3216918,45.8220096 41.2683064,46.2624393 41.1615355,46.7295617 C41.0013793,47.2233768 40.7010863,47.757231 40.2606566,48.3311242 L28.8695433,62.5850305 L28.8695433,66.5288781 L45.5057738,66.5288781 Z M58.3570433,66.7691125 C60.8528116,66.7424198 62.9615355,65.921619 64.6832152,64.3067101 C66.3782022,62.7318404 67.239042,60.6297896 67.2657348,58.0005578 C67.239042,55.1844771 66.0912556,52.995675 63.8223754,51.4341515 C64.6898884,50.6734094 65.3972452,49.7992232 65.9444457,48.811593 C66.4649535,47.8906945 66.7252074,46.7962935 66.7252074,45.5283898 C66.6985147,43.0993534 65.9044066,41.0907271 64.3428832,39.5025109 C62.7947061,37.9276411 60.7994262,37.1268599 58.3570433,37.1001672 C55.9546996,37.1268599 53.9927855,37.9276411 52.4713012,39.5025109 C50.8563923,41.0907271 50.0355915,43.0993534 50.0088988,45.5283898 C50.0088988,46.7962935 50.2958454,47.8906945 50.8697387,48.811593 C51.3635538,49.7992232 52.0442178,50.6734094 52.9117308,51.4341515 C50.6428506,52.995675 49.4950642,55.1844771 49.4683715,58.0005578 C49.4950642,60.6297896 50.3825967,62.7318404 52.1309691,64.3067101 C53.7992634,65.921619 55.8746215,66.7424198 58.3570433,66.7691125 Z M58.3570433,49.6323937 C57.2759887,49.6323937 56.3217243,49.265369 55.4942504,48.5313195 C54.6534301,47.8239627 54.2196736,46.7962935 54.1929808,45.4483117 C54.2196736,44.0602909 54.6534301,43.0126021 55.4942504,42.3052453 C56.3217243,41.6245812 57.2759887,41.2842492 58.3570433,41.2842492 C59.4781371,41.2842492 60.452421,41.6245812 61.2798949,42.3052453 C62.0940225,43.0126021 62.5144327,44.0602909 62.5411254,45.4483117 C62.5144327,46.7962935 62.0940225,47.8239627 61.2798949,48.5313195 C60.452421,49.265369 59.4781371,49.6323937 58.3570433,49.6323937 Z M58.3570433,62.5850305 C57.0757933,62.5850305 55.9880655,62.1379276 55.0938598,61.2437219 C54.159615,60.3895552 53.6791462,59.2951542 53.6524535,57.9605187 C53.6791462,56.5991906 54.159615,55.4914432 55.0938598,54.6372765 C55.9880655,53.7964562 57.0757933,53.3626997 58.3570433,53.336007 C59.6783324,53.3626997 60.7860798,53.7964562 61.6802855,54.6372765 C62.5878376,55.4914432 63.05496,56.5991906 63.0816527,57.9605187 C63.05496,59.2951542 62.5878376,60.3895552 61.6802855,61.2437219 C60.7860798,62.1379276 59.6783324,62.5850305 58.3570433,62.5850305 Z\" id=\"path-5\"></path>\n"
        , "<path d=\"M45.0058864,63.1203897 L45.0058864,58.9363077 L33.8550075,58.9363077 L43.2441677,47.1648233 C44.4186468,45.6700316 45.0058864,43.9616983 45.0058864,42.0398233 C44.9791937,39.6374795 44.1717393,37.6555459 42.5835231,36.0940225 C41.0219997,34.5191527 38.9866807,33.7183715 36.4775661,33.6916787 C34.2353786,33.7183715 32.3468695,34.5058064 30.8120388,36.0539834 C29.2905544,37.6421996 28.4764268,39.6508259 28.369656,42.0798623 L32.5337185,42.0798623 C32.6805283,40.7452269 33.1476507,39.7108845 33.9350856,38.976835 C34.6958278,38.2427855 35.6500921,37.8757608 36.7978786,37.8757608 C38.092475,37.9024535 39.0934515,38.3228636 39.8008083,39.1369912 C40.4814724,39.9511189 40.8218044,40.9053832 40.8218044,41.9997842 C40.8218044,42.4135212 40.768419,42.8539509 40.6616481,43.3210733 C40.5014919,43.8148884 40.2011989,44.3487425 39.7607692,44.9226358 L28.369656,59.176542 L28.369656,63.1203897 L45.0058864,63.1203897 Z M57.1364528,63.1203897 C58.4043565,60.5578897 59.5521429,58.2356241 60.5798122,56.1535928 C61.0202419,55.2727334 61.4506618,54.3952106 61.871072,53.5210244 C62.2914821,52.6468383 62.6785264,51.8527302 63.0322048,51.1387002 C63.3858832,50.4246703 63.679503,49.8207478 63.9130642,49.3269327 C64.1466254,48.8331175 64.2900987,48.512805 64.3434841,48.3659952 C64.5570257,47.832141 64.7438747,47.3383259 64.904031,46.8845498 C65.0641872,46.4307738 65.2076605,45.9703246 65.3344509,45.5032022 C65.4612412,45.0360798 65.5713487,44.5456013 65.6647731,44.0317666 C65.7581976,43.517932 65.8449489,42.934029 65.925027,42.2800577 C65.9784125,41.839628 65.9650661,41.3091104 65.884988,40.6885049 C65.8049099,40.0678994 65.641417,39.4272744 65.3945095,38.7666299 C65.1476019,38.1059854 64.8039333,37.4553506 64.3635036,36.8147256 C63.9230739,36.1741006 63.3692002,35.6135537 62.7018825,35.133085 C62.0345648,34.6526162 61.2371201,34.2855915 60.3095485,34.0320108 C59.3819769,33.77843 58.310932,33.7050251 57.0964138,33.8117959 C55.935281,33.9185668 54.9309678,34.1654743 54.0834743,34.5525186 C53.2359808,34.9395629 52.5219509,35.4000121 51.9413845,35.9338662 C51.3608181,36.4677204 50.8903591,37.0382771 50.5300075,37.6455362 C50.169656,38.2527953 49.8893825,38.8233519 49.6891872,39.3572061 C49.4889919,39.8910603 49.3521918,40.3481729 49.2787868,40.728544 C49.2053819,41.1089151 49.1620062,41.3458129 49.1486599,41.4392373 C49.1219671,41.6260863 49.1052842,41.8529743 49.098611,42.1199014 C49.0919379,42.3868285 49.0886013,42.650419 49.0886013,42.9106729 C49.0886013,43.1709268 49.0919379,43.4078246 49.098611,43.6213662 C49.1052842,43.8349079 49.1086208,43.9817178 49.1086208,44.0617959 C49.1086208,44.3153767 49.1519964,44.6657185 49.2387477,45.1128213 C49.3254991,45.5599242 49.4656358,46.0403929 49.6591579,46.5542276 C49.85268,47.0680622 50.1095974,47.5952432 50.4299099,48.1357705 C50.7502224,48.6762979 51.150613,49.1667764 51.6310817,49.6072061 C52.1115505,50.0476358 52.6821071,50.4146605 53.3427517,50.7082803 C54.0033962,51.0019001 54.7741481,51.1620564 55.6550075,51.1887491 C56.2956325,51.2154418 56.859516,51.205432 57.3466579,51.1587198 C57.8337998,51.1120075 58.2708929,50.9752074 58.6579372,50.7483194 C58.4043565,51.1887491 58.0740342,51.7960082 57.6669704,52.5700967 C57.2599066,53.3441853 56.8228135,54.1916787 56.3556911,55.1125772 C55.8885687,56.0334756 55.4147731,56.9677204 54.9343044,57.9153116 C54.4538356,58.8629027 54.0200791,59.7237425 53.6330349,60.4978311 C53.2459906,61.2719196 52.9323513,61.9025349 52.6921169,62.3896768 C52.4518825,62.8768187 52.3384385,63.1203897 52.3517849,63.1203897 L52.3517849,63.1203897 L57.1364528,63.1203897 Z M57.6463948,47.5711456 L57.3967067,47.5652139 C56.3823838,47.5118285 55.5949489,47.2882771 55.034402,46.8945596 C54.4738552,46.5008422 54.0634548,46.0270466 53.8032009,45.4731729 C53.542947,44.9192992 53.3861273,44.328723 53.3327419,43.7014444 C53.2793565,43.0741657 53.2526638,42.5002725 53.2526638,41.9797647 C53.2526638,41.8196084 53.3127224,41.5259886 53.4328395,41.0989053 C53.5529567,40.671822 53.7664984,40.2313923 54.0734645,39.7776162 C54.3804307,39.3238402 54.7975043,38.9101032 55.3246852,38.5364053 C55.8518662,38.1627074 56.5291937,37.9558389 57.3566677,37.9157998 C58.1040635,37.8757608 58.7280056,37.939156 59.2284938,38.1059854 C59.7289821,38.2728148 60.1393825,38.5030394 60.459695,38.7966592 C60.7800075,39.090279 61.0269151,39.4339476 61.2004177,39.8276651 C61.3739203,40.2213825 61.4973741,40.6317829 61.570779,41.0588662 C61.6441839,41.4859496 61.684223,41.9063597 61.6908962,42.3200967 C61.6975694,42.7338337 61.7075791,43.1075316 61.7209255,43.4411905 C61.7209255,43.6013467 61.6875596,43.8148884 61.6208278,44.0818155 C61.5540961,44.3487425 61.4439886,44.6390258 61.2905056,44.9526651 C61.1370225,45.2663044 60.9435004,45.5832803 60.7099392,45.9035928 C60.476378,46.2239053 60.1994411,46.5108519 59.8791286,46.7644327 C59.5588161,47.0180134 59.1917914,47.2215453 58.7780544,47.3750284 C58.3643174,47.5285114 57.9038682,47.5919066 57.3967067,47.5652139 L57.6463948,47.5711456 Z\" id=\"path-5\"></path>\n"
        , "<path d=\"M36.363667,66.8666288 C38.9395134,66.8399361 41.0482374,66.0191353 42.6898389,64.4042265 C44.3581332,62.8293567 45.2056267,60.6739205 45.2323194,57.9379179 C45.2323194,56.7100533 44.9720655,55.5355741 44.4515577,54.4144804 C43.9043571,53.2933866 43.0168246,52.3324491 41.78896,51.5316679 C42.9901319,50.7175403 43.824279,49.7899686 44.2914014,48.748953 C44.691792,47.7079374 44.8919874,46.6268827 44.8919874,45.505789 C44.8652946,43.2902942 44.1045525,41.3684192 42.6097608,39.740164 C41.0482374,38.0718697 38.9395134,37.2243762 36.2835889,37.1976835 C34.1882113,37.2243762 32.3864535,37.971772 30.8783155,39.439871 C29.3434847,40.9213163 28.4692985,42.7430936 28.2557569,44.905203 L32.4398389,44.905203 C32.706766,43.7040311 33.2272738,42.8164986 34.0013624,42.2426054 C34.7354118,41.6687121 35.5495394,41.3817655 36.4437452,41.3817655 C37.6849561,41.4084582 38.699279,41.8021757 39.4867139,42.5629179 C40.2741488,43.3503528 40.6812126,44.3580025 40.7079053,45.5858671 C40.7079053,46.8003853 40.3141879,47.7880155 39.526753,48.5487577 C38.7393181,49.3361926 37.6315707,49.72991 36.2035108,49.72991 L34.7621045,49.72991 L34.7621045,53.4335233 L36.5238233,53.4335233 C37.8184196,53.4335233 38.886128,53.8272408 39.7269483,54.6146757 C40.581115,55.4288033 41.0215446,56.5765897 41.0482374,58.058035 C41.0215446,59.5127876 40.581115,60.6338814 39.7269483,61.4213163 C38.886128,62.2621366 37.8517855,62.6825468 36.6239209,62.6825468 C35.4761345,62.6825468 34.555236,62.3689074 33.8612256,61.7416288 C33.1805616,61.1410429 32.6533806,60.353608 32.2796827,59.3793241 L28.0956006,59.3793241 C28.5493767,61.8083606 29.5436801,63.6635038 31.0785108,64.9447538 C32.6133415,66.2260038 34.3750603,66.8666288 36.363667,66.8666288 Z M58.3638624,66.8666288 C60.6327426,66.8399361 62.5612907,66.1058866 64.1495069,64.6644804 C65.7644157,63.2097278 66.5985629,61.1410429 66.6519483,58.4584257 L66.6519483,45.5858671 C66.5985629,42.8899035 65.7644157,40.8145455 64.1495069,39.3597929 C62.5612907,37.9183866 60.6327426,37.1976835 58.3638624,37.1976835 C56.1617139,37.1976835 54.2665316,37.9183866 52.6783155,39.3597929 C50.9833285,40.8145455 50.1224887,42.8899035 50.0957959,45.5858671 L50.0957959,58.4584257 C50.1224887,61.1410429 50.9833285,63.2097278 52.6783155,64.6644804 C54.2665316,66.1058866 56.1617139,66.8399361 58.3638624,66.8666288 Z M58.3638624,62.6825468 C55.6945915,62.6558541 54.3332634,61.1677356 54.279878,58.2181913 L54.279878,45.846121 C54.3332634,42.9232694 55.6945915,41.4351509 58.3638624,41.3817655 C61.0197868,41.4351509 62.3877881,42.9232694 62.4678663,45.846121 L62.4678663,58.2181913 C62.3877881,61.1677356 61.0197868,62.6558541 58.3638624,62.6825468 Z\" id=\"path-5\"></path>\n"
        , "<path d=\"M36.3927939,67.2831627 C38.9686403,67.25647 41.0773642,66.4356693 42.7189658,64.8207604 C44.3872601,63.2458906 45.2347536,61.0904544 45.2614463,58.3544518 C45.2614463,57.1265872 45.0011924,55.9521081 44.4806846,54.8310143 C43.933484,53.7099206 43.0459515,52.7489831 41.8180869,51.9482018 C43.0192588,51.1340742 43.8534059,50.2065026 44.3205283,49.165487 C44.7209189,48.1244713 44.9211142,47.0434166 44.9211142,45.9223229 C44.8944215,43.7068281 44.1336793,41.7849531 42.6388877,40.1566979 C41.0773642,38.4884036 38.9686403,37.6409101 36.3127158,37.6142174 C34.2173382,37.6409101 32.4155804,38.388306 30.9074424,39.8564049 C29.3726116,41.3378502 28.4984254,43.1596276 28.2848838,45.321737 L32.4689658,45.321737 C32.7358929,44.1205651 33.2564007,43.2330325 34.0304892,42.6591393 C34.7645387,42.0852461 35.5786663,41.7982995 36.4728721,41.7982995 C37.714083,41.8249922 38.7284059,42.2187096 39.5158408,42.9794518 C40.3032757,43.7668867 40.7103395,44.7745364 40.7370322,46.002401 C40.7370322,47.2169193 40.3433148,48.2045495 39.5558799,48.9652916 C38.768445,49.7527265 37.6606976,50.146444 36.2326377,50.146444 L34.7912314,50.146444 L34.7912314,53.8500573 L36.5529502,53.8500573 C37.8475465,53.8500573 38.9152549,54.2437747 39.7560752,55.0312096 C40.6102418,55.8453372 41.0506715,56.9931237 41.0773642,58.474569 C41.0506715,59.9293216 40.6102418,61.0504153 39.7560752,61.8378502 C38.9152549,62.6786706 37.8809124,63.0990807 36.6530478,63.0990807 C35.5052614,63.0990807 34.5843629,62.7854414 33.8903525,62.1581627 C33.2096885,61.5575768 32.6825075,60.7701419 32.3088096,59.7958581 L28.1247275,59.7958581 C28.5785036,62.2248945 29.572807,64.0800377 31.1076377,65.3612877 C32.6424684,66.6425377 34.4041872,67.2831627 36.3927939,67.2831627 Z M60.6952353,67.0429284 L60.6952353,37.8544518 L56.5111533,37.8544518 L52.3671103,40.877401 L52.3671103,45.321737 L56.5111533,42.2787682 L56.5111533,67.0429284 L60.6952353,67.0429284 Z\" id=\"path-5\"></path>\n"
    };

    static const QByteArrayList &monthList= {  "<path d=\"M34.9493277,29.9328819 C36.9180777,29.9328819 37.7580777,28.5416319 37.7580777,26.8091319 L37.7580777,20.0891319 L36.2355777,20.0891319 L36.2355777,26.6647569 C36.2355777,28.0822569 35.7499527,28.6072569 34.7787027,28.6072569 C34.1487027,28.6072569 33.6237027,28.3053819 33.2168277,27.5572569 L32.1537027,28.3316319 C32.7574527,29.3947569 33.6499527,29.9328819 34.9493277,29.9328819 Z M40.5274527,29.7622569 L41.3543277,27.0060069 L44.7143277,27.0060069 L45.5280777,29.7622569 L47.1424527,29.7622569 L43.9399527,20.0891319 L42.1812027,20.0891319 L38.9787027,29.7622569 L40.5274527,29.7622569 Z M44.3468277,25.8116319 L41.7087027,25.8116319 L42.1024527,24.5122569 C42.4174527,23.4622569 42.7193277,22.3991319 42.9949527,21.2966319 L43.0605777,21.2966319 C43.3493277,22.3860069 43.6380777,23.4622569 43.9662027,24.5122569 L44.3468277,25.8116319 Z M49.8593277,29.7622569 L49.8593277,25.2078819 C49.8593277,24.1578819 49.7412027,23.0422569 49.6624527,22.0447569 L49.7280777,22.0447569 L50.7387027,24.0660069 L53.9412027,29.7622569 L55.5030777,29.7622569 L55.5030777,20.0891319 L54.0593277,20.0891319 L54.0593277,24.6041319 C54.0593277,25.6541319 54.1774527,26.8222569 54.2562027,27.8197569 L54.1905777,27.8197569 L53.1799527,25.7722569 L49.9774527,20.0891319 L48.4155777,20.0891319 L48.4155777,29.7622569 L49.8593277,29.7622569 Z\" id=\"JAN\" fill=\"#E06164\" fill-rule=\"nonzero\" transform=\"translate(43.828390, 25.011007) rotate(-8.000000) translate(-43.828390, -25.011007) \"></path>\n"
                                              , "<path d=\"M34.5405751,29.7183343 L34.5405751,25.5577093 L38.1105751,25.5577093 L38.1105751,24.2845843 L34.5405751,24.2845843 L34.5405751,21.3314593 L38.7405751,21.3314593 L38.7405751,20.0452093 L33.0180751,20.0452093 L33.0180751,29.7183343 L34.5405751,29.7183343 Z M46.3005751,29.7183343 L46.3005751,28.4320843 L41.9693251,28.4320843 L41.9693251,25.3083343 L45.5130751,25.3083343 L45.5130751,24.0220843 L41.9693251,24.0220843 L41.9693251,21.3314593 L46.1562001,21.3314593 L46.1562001,20.0452093 L40.4468251,20.0452093 L40.4468251,29.7183343 L46.3005751,29.7183343 Z M51.5505751,29.7183343 C53.6899501,29.7183343 55.2518251,28.7995843 55.2518251,26.8833343 C55.2518251,25.5708343 54.4512001,24.8227093 53.3487001,24.5864593 L53.3487001,24.5208343 C54.2149501,24.2320843 54.7268251,23.3527093 54.7268251,22.4339593 C54.7268251,20.6883343 53.2962001,20.0452093 51.3143251,20.0452093 L48.3218251,20.0452093 L48.3218251,29.7183343 L51.5505751,29.7183343 Z M51.1437001,24.0877093 L49.8443251,24.0877093 L49.8443251,21.2395843 L51.1830751,21.2395843 C52.5480751,21.2395843 53.2305751,21.6202093 53.2305751,22.6308343 C53.2305751,23.5364593 52.6137001,24.0877093 51.1437001,24.0877093 Z M51.3799501,28.5239593 L49.8443251,28.5239593 L49.8443251,25.2295843 L51.3799501,25.2295843 C52.9155751,25.2295843 53.7555751,25.7152093 53.7555751,26.8045843 C53.7555751,27.9858343 52.8893251,28.5239593 51.3799501,28.5239593 Z\" id=\"FEB\" fill=\"#E06164\" fill-rule=\"nonzero\" transform=\"translate(44.134950, 24.881772) rotate(-8.000000) translate(-44.134950, -24.881772) \"></path>\n"
                                              , "<path d=\"M34.3644901,29.4525572 L34.3644901,24.6750572 C34.3644901,23.8088072 34.2463651,22.5619322 34.1807401,21.6825572 L34.2332401,21.6825572 L34.9944901,23.9138072 L36.6876151,28.5206822 L37.6326151,28.5206822 L39.3126151,23.9138072 L40.0869901,21.6825572 L40.1394901,21.6825572 C40.0607401,22.5619322 39.9426151,23.8088072 39.9426151,24.6750572 L39.9426151,29.4525572 L41.3601151,29.4525572 L41.3601151,19.7794322 L39.6013651,19.7794322 L37.8557401,24.6750572 C37.6326151,25.3181822 37.4488651,25.9744322 37.2257401,26.6175572 L37.1601151,26.6175572 C36.9369901,25.9744322 36.7401151,25.3181822 36.5169901,24.6750572 L34.7451151,19.7794322 L32.9994901,19.7794322 L32.9994901,29.4525572 L34.3644901,29.4525572 Z M44.1688651,29.4525572 L44.9957401,26.6963072 L48.3557401,26.6963072 L49.1694901,29.4525572 L50.7838651,29.4525572 L47.5813651,19.7794322 L45.8226151,19.7794322 L42.6201151,29.4525572 L44.1688651,29.4525572 Z M47.9882401,25.5019322 L45.3501151,25.5019322 L45.7438651,24.2025572 C46.0588651,23.1525572 46.3607401,22.0894322 46.6363651,20.9869322 L46.7019901,20.9869322 C46.9907401,22.0763072 47.2794901,23.1525572 47.6076151,24.2025572 L47.9882401,25.5019322 Z M53.5794901,29.4525572 L53.5794901,25.5544322 L55.1413651,25.5544322 L57.3332401,29.4525572 L59.0526151,29.4525572 L56.6901151,25.3575572 C57.9107401,24.9769322 58.7113651,24.0844322 58.7113651,22.5881822 C58.7113651,20.5013072 57.2151151,19.7794322 55.2201151,19.7794322 L52.0569901,19.7794322 L52.0569901,29.4525572 L53.5794901,29.4525572 Z M55.0363651,24.3338072 L53.5794901,24.3338072 L53.5794901,21.0131822 L55.0363651,21.0131822 C56.4407401,21.0131822 57.2019901,21.4200572 57.2019901,22.5881822 C57.2019901,23.7563072 56.4407401,24.3338072 55.0363651,24.3338072 Z\" id=\"MAR\" fill=\"#E06164\" fill-rule=\"nonzero\" transform=\"translate(46.026053, 24.615995) rotate(-8.000000) translate(-46.026053, -24.615995) \"></path>\n"
                                              , "<path d=\"M33.2929976,29.7082878 L34.1198726,26.9520378 L37.4798726,26.9520378 L38.2936226,29.7082878 L39.9079976,29.7082878 L36.7054976,20.0351628 L34.9467476,20.0351628 L31.7442476,29.7082878 L33.2929976,29.7082878 Z M37.1123726,25.7576628 L34.4742476,25.7576628 L34.8679976,24.4582878 C35.1829976,23.4082878 35.4848726,22.3451628 35.7604976,21.2426628 L35.8261226,21.2426628 C36.1148726,22.3320378 36.4036226,23.4082878 36.7317476,24.4582878 L37.1123726,25.7576628 Z M42.7036226,29.7082878 L42.7036226,26.0464128 L44.1604976,26.0464128 C46.2604976,26.0464128 47.7961226,25.0751628 47.7961226,22.9620378 C47.7961226,20.7832878 46.2604976,20.0351628 44.1079976,20.0351628 L41.1811226,20.0351628 L41.1811226,29.7082878 L42.7036226,29.7082878 Z M44.0161226,24.8257878 L42.7036226,24.8257878 L42.7036226,21.2689128 L43.9636226,21.2689128 C45.4992476,21.2689128 46.2998726,21.6889128 46.2998726,22.9620378 C46.2998726,24.2351628 45.5648726,24.8257878 44.0161226,24.8257878 Z M51.1954976,29.7082878 L51.1954976,25.8101628 L52.7573726,25.8101628 L54.9492476,29.7082878 L56.6686226,29.7082878 L54.3061226,25.6132878 C55.5267476,25.2326628 56.3273726,24.3401628 56.3273726,22.8439128 C56.3273726,20.7570378 54.8311226,20.0351628 52.8361226,20.0351628 L49.6729976,20.0351628 L49.6729976,29.7082878 L51.1954976,29.7082878 Z M52.6523726,24.5895378 L51.1954976,24.5895378 L51.1954976,21.2689128 L52.6523726,21.2689128 C54.0567476,21.2689128 54.8179976,21.6757878 54.8179976,22.8439128 C54.8179976,24.0120378 54.0567476,24.5895378 52.6523726,24.5895378 Z\" id=\"APR\" fill=\"#E06164\" fill-rule=\"nonzero\" transform=\"translate(44.206435, 24.871725) rotate(-8.000000) translate(-44.206435, -24.871725) \"></path>\n"
                                              , "<path d=\"M34.3692162,29.5201431 L34.3692162,24.7426431 C34.3692162,23.8763931 34.2510912,22.6295181 34.1854662,21.7501431 L34.2379662,21.7501431 L34.9992162,23.9813931 L36.6923412,28.5882681 L37.6373412,28.5882681 L39.3173412,23.9813931 L40.0917162,21.7501431 L40.1442162,21.7501431 C40.0654662,22.6295181 39.9473412,23.8763931 39.9473412,24.7426431 L39.9473412,29.5201431 L41.3648412,29.5201431 L41.3648412,19.8470181 L39.6060912,19.8470181 L37.8604662,24.7426431 C37.6373412,25.3857681 37.4535912,26.0420181 37.2304662,26.6851431 L37.1648412,26.6851431 C36.9417162,26.0420181 36.7448412,25.3857681 36.5217162,24.7426431 L34.7498412,19.8470181 L33.0042162,19.8470181 L33.0042162,29.5201431 L34.3692162,29.5201431 Z M44.1735912,29.5201431 L45.0004662,26.7638931 L48.3604662,26.7638931 L49.1742162,29.5201431 L50.7885912,29.5201431 L47.5860912,19.8470181 L45.8273412,19.8470181 L42.6248412,29.5201431 L44.1735912,29.5201431 Z M47.9929662,25.5695181 L45.3548412,25.5695181 L45.7485912,24.2701431 C46.0635912,23.2201431 46.3654662,22.1570181 46.6410912,21.0545181 L46.7067162,21.0545181 C46.9954662,22.1438931 47.2842162,23.2201431 47.6123412,24.2701431 L47.9929662,25.5695181 Z M55.1723412,29.5201431 L55.1723412,25.8582681 L58.0860912,19.8470181 L56.4979662,19.8470181 L55.3692162,22.4195181 C55.0804662,23.1413931 54.7654662,23.8107681 54.4504662,24.5457681 L54.3979662,24.5457681 C54.0698412,23.8107681 53.7942162,23.1413931 53.4923412,22.4195181 L52.3767162,19.8470181 L50.7492162,19.8470181 L53.6498412,25.8582681 L53.6498412,29.5201431 L55.1723412,29.5201431 Z\" id=\"MAY\" fill=\"#E06164\" fill-rule=\"nonzero\" transform=\"translate(45.545154, 24.683581) rotate(-8.000000) translate(-45.545154, -24.683581) \"></path>\n"
                                              , "<path d=\"M34.9422386,29.831503 C36.9109886,29.831503 37.7509886,28.440253 37.7509886,26.707753 L37.7509886,19.987753 L36.2284886,19.987753 L36.2284886,26.563378 C36.2284886,27.980878 35.7428636,28.505878 34.7716136,28.505878 C34.1416136,28.505878 33.6166136,28.204003 33.2097386,27.455878 L32.1466136,28.230253 C32.7503636,29.293378 33.6428636,29.831503 34.9422386,29.831503 Z M43.7884886,29.831503 C45.9278636,29.831503 47.3716136,28.663378 47.3716136,25.513378 L47.3716136,19.987753 L45.9016136,19.987753 L45.9016136,25.618378 C45.9016136,27.797128 45.0091136,28.505878 43.7884886,28.505878 C42.5809886,28.505878 41.7147386,27.797128 41.7147386,25.618378 L41.7147386,19.987753 L40.1922386,19.987753 L40.1922386,25.513378 C40.1922386,28.663378 41.6491136,29.831503 43.7884886,29.831503 Z M51.3091136,29.660878 L51.3091136,25.106503 C51.3091136,24.056503 51.1909886,22.940878 51.1122386,21.943378 L51.1778636,21.943378 L52.1884886,23.964628 L55.3909886,29.660878 L56.9528636,29.660878 L56.9528636,19.987753 L55.5091136,19.987753 L55.5091136,24.502753 C55.5091136,25.552753 55.6272386,26.720878 55.7059886,27.718378 L55.6403636,27.718378 L54.6297386,25.670878 L51.4272386,19.987753 L49.8653636,19.987753 L49.8653636,29.660878 L51.3091136,29.660878 Z\" id=\"JUN\" fill=\"#E06164\" fill-rule=\"nonzero\" transform=\"translate(44.549739, 24.909628) rotate(-8.000000) translate(-44.549739, -24.909628) \"></path>\n"
                                              , "<path d=\"M34.9493916,29.9337952 C36.9181416,29.9337952 37.7581416,28.5425452 37.7581416,26.8100452 L37.7581416,20.0900452 L36.2356416,20.0900452 L36.2356416,26.6656702 C36.2356416,28.0831702 35.7500166,28.6081702 34.7787666,28.6081702 C34.1487666,28.6081702 33.6237666,28.3062952 33.2168916,27.5581702 L32.1537666,28.3325452 C32.7575166,29.3956702 33.6500166,29.9337952 34.9493916,29.9337952 Z M43.7956416,29.9337952 C45.9350166,29.9337952 47.3787666,28.7656702 47.3787666,25.6156702 L47.3787666,20.0900452 L45.9087666,20.0900452 L45.9087666,25.7206702 C45.9087666,27.8994202 45.0162666,28.6081702 43.7956416,28.6081702 C42.5881416,28.6081702 41.7218916,27.8994202 41.7218916,25.7206702 L41.7218916,20.0900452 L40.1993916,20.0900452 L40.1993916,25.6156702 C40.1993916,28.7656702 41.6562666,29.9337952 43.7956416,29.9337952 Z M55.4900166,29.7631702 L55.4900166,28.4769202 L51.3950166,28.4769202 L51.3950166,20.0900452 L49.8725166,20.0900452 L49.8725166,29.7631702 L55.4900166,29.7631702 Z\" id=\"JUL\" fill=\"#E06164\" fill-rule=\"nonzero\" transform=\"translate(43.821892, 25.011920) rotate(-8.000000) translate(-43.821892, -25.011920) \"></path>\n"
                                              , "<path d=\"M33.2875051,29.6297419 L34.1143801,26.8734919 L37.4743801,26.8734919 L38.2881301,29.6297419 L39.9025051,29.6297419 L36.7000051,19.9566169 L34.9412551,19.9566169 L31.7387551,29.6297419 L33.2875051,29.6297419 Z M37.1068801,25.6791169 L34.4687551,25.6791169 L34.8625051,24.3797419 C35.1775051,23.3297419 35.4793801,22.2666169 35.7550051,21.1641169 L35.8206301,21.1641169 C36.1093801,22.2534919 36.3981301,23.3297419 36.7262551,24.3797419 L37.1068801,25.6791169 Z M44.7193801,29.8003669 C46.8587551,29.8003669 48.3025051,28.6322419 48.3025051,25.4822419 L48.3025051,19.9566169 L46.8325051,19.9566169 L46.8325051,25.5872419 C46.8325051,27.7659919 45.9400051,28.4747419 44.7193801,28.4747419 C43.5118801,28.4747419 42.6456301,27.7659919 42.6456301,25.5872419 L42.6456301,19.9566169 L41.1231301,19.9566169 L41.1231301,25.4822419 C41.1231301,28.6322419 42.5800051,29.8003669 44.7193801,29.8003669 Z M54.7337551,29.8003669 C56.0593801,29.8003669 57.1487551,29.3147419 57.7918801,28.6716169 L57.7918801,24.4847419 L54.4975051,24.4847419 L54.4975051,25.7316169 L56.4137551,25.7316169 L56.4137551,28.0022419 C56.0725051,28.3041169 55.4818801,28.4747419 54.8912551,28.4747419 C52.8962551,28.4747419 51.8331301,27.0703669 51.8331301,24.7734919 C51.8331301,22.5028669 53.0275051,21.1116169 54.8256301,21.1116169 C55.7575051,21.1116169 56.3481301,21.4922419 56.8337551,21.9778669 L57.6475051,21.0066169 C57.0568801,20.3897419 56.1381301,19.7859919 54.7731301,19.7859919 C52.2137551,19.7859919 50.2712551,21.6759919 50.2712551,24.8259919 C50.2712551,27.9891169 52.1612551,29.8003669 54.7337551,29.8003669 Z\" id=\"AUG\" fill=\"#E06164\" fill-rule=\"nonzero\" transform=\"translate(44.765318, 24.793179) rotate(-8.000000) translate(-44.765318, -24.793179) \"></path>\n"
                                              , "<path d=\"M35.7764965,29.9200123 C37.8896215,29.9200123 39.1758715,28.6600123 39.1758715,27.1112623 C39.1758715,25.6937623 38.3489965,24.9850123 37.2071215,24.4993873 L35.8814965,23.9350123 C35.0939965,23.6200123 34.3196215,23.3050123 34.3196215,22.4650123 C34.3196215,21.7037623 34.9627465,21.2312623 35.9471215,21.2312623 C36.8002465,21.2312623 37.4696215,21.5593873 38.0733715,22.0975123 L38.8739965,21.1262623 C38.1389965,20.3781373 37.0758715,19.9056373 35.9471215,19.9056373 C34.1096215,19.9056373 32.7708715,21.0475123 32.7708715,22.5700123 C32.7708715,23.9743873 33.8077465,24.7093873 34.7396215,25.1031373 L36.0783715,25.6806373 C36.9708715,26.0743873 37.6271215,26.3500123 37.6271215,27.2293873 C37.6271215,28.0431373 36.9708715,28.5943873 35.8027465,28.5943873 C34.8708715,28.5943873 33.9258715,28.1481373 33.2302465,27.4525123 L32.3377465,28.5025123 C33.2171215,29.3950123 34.4508715,29.9200123 35.7764965,29.9200123 Z M46.8539965,29.7493873 L46.8539965,28.4631373 L42.5227465,28.4631373 L42.5227465,25.3393873 L46.0664965,25.3393873 L46.0664965,24.0531373 L42.5227465,24.0531373 L42.5227465,21.3625123 L46.7096215,21.3625123 L46.7096215,20.0762623 L41.0002465,20.0762623 L41.0002465,29.7493873 L46.8539965,29.7493873 Z M50.3977465,29.7493873 L50.3977465,26.0875123 L51.8546215,26.0875123 C53.9546215,26.0875123 55.4902465,25.1162623 55.4902465,23.0031373 C55.4902465,20.8243873 53.9546215,20.0762623 51.8021215,20.0762623 L48.8752465,20.0762623 L48.8752465,29.7493873 L50.3977465,29.7493873 Z M51.7102465,24.8668873 L50.3977465,24.8668873 L50.3977465,21.3100123 L51.6577465,21.3100123 C53.1933715,21.3100123 53.9939965,21.7300123 53.9939965,23.0031373 C53.9939965,24.2762623 53.2589965,24.8668873 51.7102465,24.8668873 Z\" id=\"SEP\" fill=\"#E06164\" fill-rule=\"nonzero\" transform=\"translate(43.913997, 24.912825) rotate(-8.000000) translate(-43.913997, -24.912825) \"></path>\n"
                                              , "<path d=\"M36.6835591,29.7528741 C39.1641841,29.7528741 40.8966841,27.8234991 40.8966841,24.7128741 C40.8966841,21.6022491 39.1641841,19.7384991 36.6835591,19.7384991 C34.2029341,19.7384991 32.4835591,21.6022491 32.4835591,24.7128741 C32.4835591,27.8234991 34.2029341,29.7528741 36.6835591,29.7528741 Z M36.6835591,28.4272491 C35.0823091,28.4272491 34.0454341,26.9703741 34.0454341,24.7128741 C34.0454341,22.4422491 35.0823091,21.0641241 36.6835591,21.0641241 C38.2848091,21.0641241 39.3348091,22.4422491 39.3348091,24.7128741 C39.3348091,26.9703741 38.2848091,28.4272491 36.6835591,28.4272491 Z M46.6716841,29.7528741 C47.9185591,29.7528741 48.9029341,29.2541241 49.6773091,28.3616241 L48.8635591,27.4034991 C48.2860591,28.0334991 47.6166841,28.4272491 46.7241841,28.4272491 C45.0179341,28.4272491 43.9416841,27.0228741 43.9416841,24.7259991 C43.9416841,22.4553741 45.0966841,21.0641241 46.7635591,21.0641241 C47.5510591,21.0641241 48.1548091,21.4184991 48.6535591,21.9303741 L49.4804341,20.9591241 C48.8898091,20.3159991 47.9448091,19.7384991 46.7373091,19.7384991 C44.2960591,19.7384991 42.3798091,21.6284991 42.3798091,24.7784991 C42.3798091,27.9416241 44.2435591,29.7528741 46.6716841,29.7528741 Z M54.8748091,29.5822491 L54.8748091,21.1953741 L57.7229341,21.1953741 L57.7229341,19.9091241 L50.5173091,19.9091241 L50.5173091,21.1953741 L53.3523091,21.1953741 L53.3523091,29.5822491 L54.8748091,29.5822491 Z\" id=\"OCT\" fill=\"#E06164\" fill-rule=\"nonzero\" transform=\"translate(45.103247, 24.745687) rotate(-8.000000) translate(-45.103247, -24.745687) \"></path>\n"
                                              , "<path d=\"M34.4431124,29.4507305 L34.4431124,24.8963555 C34.4431124,23.8463555 34.3249874,22.7307305 34.2462374,21.7332305 L34.3118624,21.7332305 L35.3224874,23.7544805 L38.5249874,29.4507305 L40.0868624,29.4507305 L40.0868624,19.7776055 L38.6431124,19.7776055 L38.6431124,24.2926055 C38.6431124,25.3426055 38.7612374,26.5107305 38.8399874,27.5082305 L38.7743624,27.5082305 L37.7637374,25.4607305 L34.5612374,19.7776055 L32.9993624,19.7776055 L32.9993624,29.4507305 L34.4431124,29.4507305 Z M46.3081124,29.6213555 C48.7887374,29.6213555 50.5212374,27.6919805 50.5212374,24.5813555 C50.5212374,21.4707305 48.7887374,19.6069805 46.3081124,19.6069805 C43.8274874,19.6069805 42.1081124,21.4707305 42.1081124,24.5813555 C42.1081124,27.6919805 43.8274874,29.6213555 46.3081124,29.6213555 Z M46.3081124,28.2957305 C44.7068624,28.2957305 43.6699874,26.8388555 43.6699874,24.5813555 C43.6699874,22.3107305 44.7068624,20.9326055 46.3081124,20.9326055 C47.9093624,20.9326055 48.9593624,22.3107305 48.9593624,24.5813555 C48.9593624,26.8388555 47.9093624,28.2957305 46.3081124,28.2957305 Z M56.0599874,29.4507305 L59.0787374,19.7776055 L57.5299874,19.7776055 L56.0993624,24.7913555 C55.7843624,25.8938555 55.5612374,26.8388555 55.2199874,27.9544805 L55.1543624,27.9544805 C54.8262374,26.8388555 54.6031124,25.8938555 54.2749874,24.7913555 L52.8443624,19.7776055 L51.2299874,19.7776055 L54.2618624,29.4507305 L56.0599874,29.4507305 Z\" id=\"NOV\" fill=\"#E06164\" fill-rule=\"nonzero\" transform=\"translate(46.039050, 24.614168) rotate(-8.000000) translate(-46.039050, -24.614168) \"></path>\n"
                                              , "<path d=\"M33.7993832,31.2184352 L36.359845,30.8585857 C39.2192441,30.4567234 40.6547743,28.5054465 40.2182057,25.3990994 C39.7798104,22.279755 37.8730053,20.8777366 34.9356226,21.2905588 L32.4531444,21.6394484 L33.7993832,31.2184352 Z M35.1335348,29.7718037 L34.134359,22.6622978 L35.0051759,22.5399125 C37.0587443,22.2513023 38.3224304,23.2267996 38.6585335,25.618297 C38.9928099,27.9967971 38.0579202,29.3608081 36.0043518,29.6494183 L35.1335348,29.7718037 Z M42.8844738,29.941609 L48.6812555,29.1269244 L48.5022441,27.8531921 L44.2131455,28.4559856 L43.7784035,25.3626357 L47.287666,24.8694411 L47.1086546,23.5957088 L43.5993921,24.0889034 L43.2249295,21.4244634 L47.3710581,20.841763 L47.1920467,19.5680307 L41.538235,20.3626222 L42.8844738,29.941609 Z M54.4367973,28.4903376 C55.6715378,28.3168062 56.5769203,27.6859114 57.2195471,26.694325 L56.2803713,25.8587765 C55.7961705,26.5630179 55.1881092,27.0460949 54.304295,27.1703069 C52.6146501,27.407771 51.3534229,26.1668484 51.0337596,23.8923264 C50.7177497,21.643799 51.6678848,20.1053436 53.3185379,19.8733594 C54.098374,19.7637606 54.7455678,20.0306611 55.3107032,20.468142 L55.9943592,19.3912653 C55.3199765,18.8365983 54.3038007,18.3962371 53.108052,18.5642886 C50.6905601,18.9040449 49.055996,21.042342 49.4943913,24.1616864 C49.9346132,27.2940281 52.0323026,28.8282673 54.4367973,28.4903376 Z\" id=\"形状结合\" fill=\"#E06164\" fill-rule=\"nonzero\"></path>\n"
                                           };

    static const QByteArrayList &weekList= {  "<path d=\"M47.4674452,78.7519461 L47.4674452,76.0219461 C47.4674452,75.5269461 47.3999452,74.8144461 47.3624452,74.3119461 L47.3924452,74.3119461 L47.8274452,75.5869461 L48.7949452,78.2194461 L49.3349452,78.2194461 L50.2949452,75.5869461 L50.7374452,74.3119461 L50.7674452,74.3119461 C50.7224452,74.8144461 50.6549452,75.5269461 50.6549452,76.0219461 L50.6549452,78.7519461 L51.4649452,78.7519461 L51.4649452,73.2244461 L50.4599452,73.2244461 L49.4624452,76.0219461 L49.1024452,77.1319461 L49.1024452,77.1319461 L49.0649452,77.1319461 L48.6974452,76.0219461 L48.6974452,76.0219461 L47.6849452,73.2244461 L46.6874452,73.2244461 L46.6874452,78.7519461 L47.4674452,78.7519461 Z M55.0124452,78.8494461 C56.4299452,78.8494461 57.4199452,77.7469461 57.4199452,75.9694461 C57.4199452,74.1919461 56.4299452,73.1269461 55.0124452,73.1269461 C53.5949452,73.1269461 52.6124452,74.1919461 52.6124452,75.9694461 C52.6124452,77.7469461 53.5949452,78.8494461 55.0124452,78.8494461 Z M55.0124452,78.0919461 C54.0974452,78.0919461 53.5049452,77.2594461 53.5049452,75.9694461 C53.5049452,74.6719461 54.0974452,73.8844461 55.0124452,73.8844461 C55.9274452,73.8844461 56.5274452,74.6719461 56.5274452,75.9694461 C56.5274452,77.2594461 55.9274452,78.0919461 55.0124452,78.0919461 Z M59.3924452,78.7519461 L59.3924452,76.1494461 C59.3924452,75.5494461 59.3249452,74.9119461 59.2799452,74.3419461 L59.3174452,74.3419461 L59.8949452,75.4969461 L61.7249452,78.7519461 L62.6174452,78.7519461 L62.6174452,73.2244461 L61.7924452,73.2244461 L61.7924452,75.8044461 C61.7924452,76.4044461 61.8599452,77.0719461 61.9049452,77.6419461 L61.8674452,77.6419461 L61.2899452,76.4719461 L59.4599452,73.2244461 L58.5674452,73.2244461 L58.5674452,78.7519461 L59.3924452,78.7519461 Z\" id=\"MON\" fill=\"#2D394F\" fill-rule=\"nonzero\" transform=\"translate(54.652445, 75.988196) rotate(-8.000000) translate(-54.652445, -75.988196) \"></path>\n"
                                             , "<path d=\"M44.0870193,78.2927006 L44.0870193,73.5002006 L45.7145193,73.5002006 L45.7145193,72.7652006 L41.5970193,72.7652006 L41.5970193,73.5002006 L43.2170193,73.5002006 L43.2170193,78.2927006 L44.0870193,78.2927006 Z M47.5370193,78.2927006 L47.5370193,75.7802006 L49.8995193,75.7802006 L49.8995193,78.2927006 L50.7695193,78.2927006 L50.7695193,72.7652006 L49.8995193,72.7652006 L49.8995193,75.0227006 L47.5370193,75.0227006 L47.5370193,72.7652006 L46.6670193,72.7652006 L46.6670193,78.2927006 L47.5370193,78.2927006 Z M54.2420193,78.3902006 C55.4645193,78.3902006 56.2895193,77.7227006 56.2895193,75.9227006 L56.2895193,72.7652006 L55.4495193,72.7652006 L55.4495193,75.9827006 C55.4495193,77.2277006 54.9395193,77.6327006 54.2420193,77.6327006 C53.5520193,77.6327006 53.0570193,77.2277006 53.0570193,75.9827006 L53.0570193,72.7652006 L52.1870193,72.7652006 L52.1870193,75.9227006 C52.1870193,77.7227006 53.0195193,78.3902006 54.2420193,78.3902006 Z M59.2895193,78.3902006 C60.4970193,78.3902006 61.2320193,77.6702006 61.2320193,76.7852006 C61.2320193,75.9752006 60.7595193,75.5702006 60.1070193,75.2927006 L59.3495193,74.9702006 C58.8995193,74.7902006 58.4570193,74.6102006 58.4570193,74.1302006 C58.4570193,73.6952006 58.8245193,73.4252006 59.3870193,73.4252006 C59.8745193,73.4252006 60.2570193,73.6127006 60.6020193,73.9202006 L61.0595193,73.3652006 C60.6395193,72.9377006 60.0320193,72.6677006 59.3870193,72.6677006 C58.3370193,72.6677006 57.5720193,73.3202006 57.5720193,74.1902006 C57.5720193,74.9927006 58.1645193,75.4127006 58.6970193,75.6377006 L59.4620193,75.9677006 C59.9720193,76.1927006 60.3470193,76.3502006 60.3470193,76.8527006 C60.3470193,77.3177006 59.9720193,77.6327006 59.3045193,77.6327006 C58.7720193,77.6327006 58.2320193,77.3777006 57.8345193,76.9802006 L57.3245193,77.5802006 C57.8270193,78.0902006 58.5320193,78.3902006 59.2895193,78.3902006 Z\" id=\"THUS\" fill=\"#2D394F\" fill-rule=\"nonzero\" transform=\"translate(51.414519, 75.528951) rotate(-8.000000) translate(-51.414519, -75.528951) \"></path>\n"
                                             , "<path d=\"M48.3132114,78.8344062 L49.0407114,75.7669062 C49.1307114,75.3319062 49.2207114,74.9194062 49.3032114,74.4994062 L49.3332114,74.4994062 C49.4082114,74.9194062 49.4907114,75.3319062 49.5882114,75.7669062 L50.3307114,78.8344062 L51.4032114,78.8344062 L52.4907114,73.3069062 L51.6582114,73.3069062 L51.1332114,76.1869062 C51.0432114,76.7644062 50.9457114,77.3569062 50.8557114,77.9569062 L50.8182114,77.9569062 C50.6907114,77.3569062 50.5707114,76.7644062 50.4432114,76.1869062 L49.7307114,73.3069062 L48.9582114,73.3069062 L48.2457114,76.1869062 C48.1182114,76.7719062 47.9907114,77.3644062 47.8707114,77.9569062 L47.8407114,77.9569062 C47.7432114,77.3644062 47.6457114,76.7719062 47.5482114,76.1869062 L47.0307114,73.3069062 L46.1382114,73.3069062 L47.2632114,78.8344062 L48.3132114,78.8344062 Z M56.7432114,78.8344062 L56.7432114,78.0994062 L54.2682114,78.0994062 L54.2682114,76.3144062 L56.2932114,76.3144062 L56.2932114,75.5794062 L54.2682114,75.5794062 L54.2682114,74.0419062 L56.6607114,74.0419062 L56.6607114,73.3069062 L53.3982114,73.3069062 L53.3982114,78.8344062 L56.7432114,78.8344062 Z M59.3757114,78.8344062 C61.0257114,78.8344062 61.9932114,77.8444062 61.9932114,76.0519062 C61.9932114,74.2519062 61.0257114,73.3069062 59.3307114,73.3069062 L57.8982114,73.3069062 L57.8982114,78.8344062 L59.3757114,78.8344062 Z M59.2707114,78.1219062 L58.7682114,78.1219062 L58.7682114,74.0194062 L59.2707114,74.0194062 C60.4557114,74.0194062 61.0932114,74.6719062 61.0932114,76.0519062 C61.0932114,77.4244062 60.4557114,78.1219062 59.2707114,78.1219062 Z\" id=\"WED\" fill=\"#2D394F\" fill-rule=\"nonzero\" transform=\"translate(54.065711, 76.070656) rotate(-8.000000) translate(-54.065711, -76.070656) \"></path>\n"
                                             , "<path d=\"M44.129459,78.5291427 L44.129459,73.7366427 L45.756959,73.7366427 L45.756959,73.0016427 L41.639459,73.0016427 L41.639459,73.7366427 L43.259459,73.7366427 L43.259459,78.5291427 L44.129459,78.5291427 Z M47.579459,78.5291427 L47.579459,76.0166427 L49.941959,76.0166427 L49.941959,78.5291427 L50.811959,78.5291427 L50.811959,73.0016427 L49.941959,73.0016427 L49.941959,75.2591427 L47.579459,75.2591427 L47.579459,73.0016427 L46.709459,73.0016427 L46.709459,78.5291427 L47.579459,78.5291427 Z M54.284459,78.6266427 C55.506959,78.6266427 56.331959,77.9591427 56.331959,76.1591427 L56.331959,73.0016427 L55.491959,73.0016427 L55.491959,76.2191427 C55.491959,77.4641427 54.981959,77.8691427 54.284459,77.8691427 C53.594459,77.8691427 53.099459,77.4641427 53.099459,76.2191427 L53.099459,73.0016427 L52.229459,73.0016427 L52.229459,76.1591427 C52.229459,77.9591427 53.061959,78.6266427 54.284459,78.6266427 Z M58.626959,78.5291427 L58.626959,76.3016427 L59.519459,76.3016427 L60.771959,78.5291427 L61.754459,78.5291427 L60.404459,76.1891427 C61.101959,75.9716427 61.559459,75.4616427 61.559459,74.6066427 C61.559459,73.4141427 60.704459,73.0016427 59.564459,73.0016427 L57.756959,73.0016427 L57.756959,78.5291427 L58.626959,78.5291427 Z M59.459459,75.6041427 L58.626959,75.6041427 L58.626959,73.7066427 L59.459459,73.7066427 C60.261959,73.7066427 60.696959,73.9391427 60.696959,74.6066427 C60.696959,75.2741427 60.261959,75.6041427 59.459459,75.6041427 Z\" id=\"THUR\" fill=\"#2D394F\" fill-rule=\"nonzero\" transform=\"translate(51.696959, 75.814143) rotate(-8.000000) translate(-51.696959, -75.814143) \"></path>\n"
                                             , "<path d=\"M47.5861301,79.1621588 L47.5861301,76.7846588 L49.6261301,76.7846588 L49.6261301,76.0571588 L47.5861301,76.0571588 L47.5861301,74.3696588 L49.9861301,74.3696588 L49.9861301,73.6346588 L46.7161301,73.6346588 L46.7161301,79.1621588 L47.5861301,79.1621588 Z M51.8311301,79.1621588 L51.8311301,76.9346588 L52.7236301,76.9346588 L53.9761301,79.1621588 L54.9586301,79.1621588 L53.6086301,76.8221588 C54.3061301,76.6046588 54.7636301,76.0946588 54.7636301,75.2396588 C54.7636301,74.0471588 53.9086301,73.6346588 52.7686301,73.6346588 L50.9611301,73.6346588 L50.9611301,79.1621588 L51.8311301,79.1621588 Z M52.6636301,76.2371588 L51.8311301,76.2371588 L51.8311301,74.3396588 L52.6636301,74.3396588 C53.4661301,74.3396588 53.9011301,74.5721588 53.9011301,75.2396588 C53.9011301,75.9071588 53.4661301,76.2371588 52.6636301,76.2371588 Z M56.7511301,79.1621588 L56.7511301,73.6346588 L55.8811301,73.6346588 L55.8811301,79.1621588 L56.7511301,79.1621588 Z\" id=\"FRI\" fill=\"#2D394F\" fill-rule=\"nonzero\" transform=\"translate(51.733630, 76.398409) rotate(-8.000000) translate(-51.733630, -76.398409) \"></path>\n"
                                             , "<path d=\"M48.2793422,79.0910854 C49.4868422,79.0910854 50.2218422,78.3710854 50.2218422,77.4860854 C50.2218422,76.6760854 49.7493422,76.2710854 49.0968422,75.9935854 L48.3393422,75.6710854 C47.8893422,75.4910854 47.4468422,75.3110854 47.4468422,74.8310854 C47.4468422,74.3960854 47.8143422,74.1260854 48.3768422,74.1260854 C48.8643422,74.1260854 49.2468422,74.3135854 49.5918422,74.6210854 L50.0493422,74.0660854 C49.6293422,73.6385854 49.0218422,73.3685854 48.3768422,73.3685854 C47.3268422,73.3685854 46.5618422,74.0210854 46.5618422,74.8910854 C46.5618422,75.6935854 47.1543422,76.1135854 47.6868422,76.3385854 L48.4518422,76.6685854 C48.9618422,76.8935854 49.3368422,77.0510854 49.3368422,77.5535854 C49.3368422,78.0185854 48.9618422,78.3335854 48.2943422,78.3335854 C47.7618422,78.3335854 47.2218422,78.0785854 46.8243422,77.6810854 L46.3143422,78.2810854 C46.8168422,78.7910854 47.5218422,79.0910854 48.2793422,79.0910854 Z M51.4218422,78.9935854 L51.8943422,77.4185854 L53.8143422,77.4185854 L54.2793422,78.9935854 L55.2018422,78.9935854 L53.3718422,73.4660854 L52.3668422,73.4660854 L50.5368422,78.9935854 L51.4218422,78.9935854 Z M53.6043422,76.7360854 L52.0968422,76.7360854 L52.3218422,75.9935854 C52.5018422,75.3935854 52.6743422,74.7860854 52.8318422,74.1560854 L52.8693422,74.1560854 C53.0343422,74.7785854 53.1993422,75.3935854 53.3868422,75.9935854 L53.6043422,76.7360854 Z M57.9243422,78.9935854 L57.9243422,74.2010854 L59.5518422,74.2010854 L59.5518422,73.4660854 L55.4343422,73.4660854 L55.4343422,74.2010854 L57.0543422,74.2010854 L57.0543422,78.9935854 L57.9243422,78.9935854 Z\" id=\"SAT\" fill=\"#2D394F\" fill-rule=\"nonzero\" transform=\"translate(52.933092, 76.229835) rotate(-8.000000) translate(-52.933092, -76.229835) \"></path>\n"
                                             , "<path d=\"M48.2732111,79.0034064 C49.4807111,79.0034064 50.2157111,78.2834064 50.2157111,77.3984064 C50.2157111,76.5884064 49.7432111,76.1834064 49.0907111,75.9059064 L48.3332111,75.5834064 C47.8832111,75.4034064 47.4407111,75.2234064 47.4407111,74.7434064 C47.4407111,74.3084064 47.8082111,74.0384064 48.3707111,74.0384064 C48.8582111,74.0384064 49.2407111,74.2259064 49.5857111,74.5334064 L50.0432111,73.9784064 C49.6232111,73.5509064 49.0157111,73.2809064 48.3707111,73.2809064 C47.3207111,73.2809064 46.5557111,73.9334064 46.5557111,74.8034064 C46.5557111,75.6059064 47.1482111,76.0259064 47.6807111,76.2509064 L48.4457111,76.5809064 C48.9557111,76.8059064 49.3307111,76.9634064 49.3307111,77.4659064 C49.3307111,77.9309064 48.9557111,78.2459064 48.2882111,78.2459064 C47.7557111,78.2459064 47.2157111,77.9909064 46.8182111,77.5934064 L46.3082111,78.1934064 C46.8107111,78.7034064 47.5157111,79.0034064 48.2732111,79.0034064 Z M53.2832111,79.0034064 C54.5057111,79.0034064 55.3307111,78.3359064 55.3307111,76.5359064 L55.3307111,73.3784064 L54.4907111,73.3784064 L54.4907111,76.5959064 C54.4907111,77.8409064 53.9807111,78.2459064 53.2832111,78.2459064 C52.5932111,78.2459064 52.0982111,77.8409064 52.0982111,76.5959064 L52.0982111,73.3784064 L51.2282111,73.3784064 L51.2282111,76.5359064 C51.2282111,78.3359064 52.0607111,79.0034064 53.2832111,79.0034064 Z M57.5807111,78.9059064 L57.5807111,76.3034064 C57.5807111,75.7034064 57.5132111,75.0659064 57.4682111,74.4959064 L57.5057111,74.4959064 L58.0832111,75.6509064 L59.9132111,78.9059064 L60.8057111,78.9059064 L60.8057111,73.3784064 L59.9807111,73.3784064 L59.9807111,75.9584064 C59.9807111,76.5584064 60.0482111,77.2259064 60.0932111,77.7959064 L60.0557111,77.7959064 L59.4782111,76.6259064 L57.6482111,73.3784064 L56.7557111,73.3784064 L56.7557111,78.9059064 L57.5807111,78.9059064 Z\" id=\"SUN\" fill=\"#2D394F\" fill-rule=\"nonzero\" transform=\"translate(53.556961, 76.142156) rotate(-8.000000) translate(-53.556961, -76.142156) \"></path>\n"};

    if (!QFile(fileName).exists()) {
        // create svg
        QFile file(fileName);
        if (!file.open(QIODevice::WriteOnly | QIODevice::Truncate))
            return false;

        file.write(QByteArray("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n"
                              "<svg width=\"96px\" height=\"96px\" viewBox=\"0 0 96 96\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n"
                              "    <title>0101</title>\n"
                              "    <defs>\n"
                              "        <radialGradient cx=\"54.3240802%\" cy=\"55.7793187%\" fx=\"54.3240802%\" fy=\"55.7793187%\" r=\"61.9689111%\" gradientTransform=\"translate(0.543241,0.557793),scale(1.000000,0.880000),rotate(-144.926693),scale(1.000000,1.264710),translate(-0.543241,-0.557793)\" id=\"radialGradient-1\">\n"
                              "            <stop stop-color=\"#000000\" offset=\"0%\"></stop>\n"
                              "            <stop stop-color=\"#000000\" stop-opacity=\"0.148218969\" offset=\"100%\"></stop>\n"
                              "        </radialGradient>\n"
                              "        <linearGradient x1=\"69.7602459%\" y1=\"71.0965544%\" x2=\"56.9833471%\" y2=\"57.7013487%\" id=\"linearGradient-2\">\n"
                              "            <stop stop-color=\"#C6C6C6\" offset=\"0%\"></stop>\n"
                              "            <stop stop-color=\"#E7E7E7\" offset=\"53.0517663%\"></stop>\n"
                              "            <stop stop-color=\"#F4F4F4\" offset=\"100%\"></stop>\n"
                              "        </linearGradient>\n"
                              "        <path d=\"M24.6973331,1.47397555 C24.6973331,1.47397555 23.4967197,8.18936447 22.1348331,10.3802256 C19.9785831,13.8489756 18.3848331,12.5364756 15.3848331,16.1927256 C12.3848331,19.8489756 14.4473331,20.5052256 9.9473331,24.3489756 C7.53576878,26.4088534 1.6973331,27.4739756 1.6973331,27.4739756 C20.3535831,27.4739756 24.6973331,11.5052256 24.6973331,1.47397555 Z\" id=\"path-3\"></path>\n"
                              "        <filter x=\"-8.7%\" y=\"-3.8%\" width=\"117.4%\" height=\"115.4%\" filterUnits=\"objectBoundingBox\" id=\"filter-4\">\n"
                              "            <feOffset dx=\"0\" dy=\"1\" in=\"SourceAlpha\" result=\"shadowOffsetOuter1\"></feOffset>\n"
                              "            <feGaussianBlur stdDeviation=\"0.5\" in=\"shadowOffsetOuter1\" result=\"shadowBlurOuter1\"></feGaussianBlur>\n"
                              "            <feColorMatrix values=\"0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.05 0\" type=\"matrix\" in=\"shadowBlurOuter1\"></feColorMatrix>\n"
                              "        </filter>\n"));
        // 日期
        file.write("        " + dayList.at(QDate::currentDate().day() - 1));
        file.write(QByteArray("        <filter x=\"-84.1%\" y=\"-17.1%\" width=\"268.1%\" height=\"148.0%\" filterUnits=\"objectBoundingBox\" id=\"filter-6\">\n"
                              "                <feOffset dx=\"0\" dy=\"2\" in=\"SourceAlpha\" result=\"shadowOffsetOuter1\"></feOffset>\n"
                              "                <feGaussianBlur stdDeviation=\"2\" in=\"shadowOffsetOuter1\" result=\"shadowBlurOuter1\"></feGaussianBlur>\n"
                              "                <feColorMatrix values=\"0 0 0 0 0.000854821203   0 0 0 0 0.168099661   0 0 0 0 0.309386322  0 0 0 0.2 0\" type=\"matrix\" in=\"shadowBlurOuter1\"></feColorMatrix>\n"
                              "        </filter>\n"
                              "    </defs>\n"
                              "    <g id=\"0101\" stroke=\"none\" stroke-width=\"1\" fill=\"none\" fill-rule=\"evenodd\">\n"
                              "        <rect id=\"矩形备份-2\" fill=\"#FFFFFF\" transform=\"translate(48.000000, 48.000000) rotate(-8.000000) translate(-48.000000, -48.000000) \" x=\"8\" y=\"8\" width=\"80\" height=\"80\" rx=\"14.625\"></rect>\n"
                              "        <g id=\"编组\" transform=\"translate(66.782981, 56.296829)\">\n"));
        // 右下角
        file.write(QByteArray("        <path d=\"M24.7611755,2.4045457 L24.7620537,12.774523 C24.7620537,20.8516875 18.2142182,27.399523 10.1370537,27.399523 L2.76205369,27.399523 C10.2787204,26.4804054 15.5648315,23.5698662 18.620387,18.6679054 C21.6750819,13.7673254 23.7220114,8.34620554 24.7611755,2.4045457 Z\" id=\"形状结合\" fill-opacity=\"0.39\" fill=\"url(#radialGradient-1)\" transform=\"translate(13.762054, 14.899523) rotate(-8.000000) translate(-13.762054, -14.899523) \"></path>\n"));

        // 背景
        file.write(QByteArray("            <g id=\"路径-11\" transform=\"translate(13.197333, 14.473976) rotate(-8.000000) translate(-13.197333, -14.473976) \">\n"
                              "                <use fill=\"black\" fill-opacity=\"1\" filter=\"url(#filter-4)\" xlink:href=\"#path-3\"></use>\n"
                              "                <use fill=\"url(#linearGradient-2)\" fill-rule=\"evenodd\" xlink:href=\"#path-3\"></use>\n"
                              "            </g>\n"
                              "        </g>\n"));
        // 月份
        file.write("        " + monthList.at(QDate::currentDate().month() - 1));
        // 日期
        file.write(QByteArray("        <g id=\"1\" fill-rule=\"nonzero\" transform=\"translate(46.076101, 52.505543) rotate(-8.000000) translate(-46.076101, -52.505543) \">\n"
                              "            <use fill=\"black\" fill-opacity=\"1\" filter=\"url(#filter-6)\" xlink:href=\"#path-5\"></use>\n"
                              "            <use fill=\"#2D394F\" xlink:href=\"#path-5\"></use>\n"
                              "            <use fill=\"#2D394F\" xlink:href=\"#path-5\"></use>\n"
                              "        </g>\n"));
        // 星期
        file.write("        " + weekList.at(QDate::currentDate().dayOfWeek() - 1));

        // 右下角
        file.write(QByteArray("    </g>\n"
                              "</svg>"));

        file.close();
    }

    return true;
}

const QPixmap IconUtils::loadSvg(const QString &fileName, int size)
{
    return loadSvg(fileName, QSize(size, size));
}

const QPixmap IconUtils::loadSvg(const QString &fileName, const QSize &size)
{
    if (!QFileInfo::exists(fileName))
        return QPixmap();

    QPixmap pixmap(size);
    DSvgRenderer renderer(fileName);
    pixmap.fill(Qt::transparent);

    QPainter painter;
    painter.begin(&pixmap);
    painter.setRenderHint(QPainter::Antialiasing, true);
    renderer.render(&painter);
    painter.end();

    return pixmap;
}

void IconUtils::tryUpdateIconCache()
{
    qInfo() << "Update theme cache manually.";
    // TODO release icon's cache.
    QIcon::setThemeSearchPaths(QIcon::themeSearchPaths());
}

// std::pair<iconSize, paddingSize>
std::pair<int, int> IconUtils::getFolderPerfectIconCell(const int size, const int iconPerRow)
{
    // (iconPerRow + 1) * paddingSize + iconPerRow * iconSize <= size
    // iconSize = perfectIconSize;
    std::pair<int, int> res;
    int maxSize = 0;
    for (float paddingPercent = 0.05; paddingPercent <= 0.08; paddingPercent += 0.01) {
        int padding = size * paddingPercent;
        int icon = (size - (iconPerRow + 1) * padding) / iconPerRow;
        int perSize = perfectIconSizeReverse(icon);
        if (perSize > maxSize) {
            res.first = perSize;
            res.second = (size - iconPerRow * perSize) / (iconPerRow + 1);
            maxSize = perSize;
        }
    }
    return res;
}
